import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { BusinessFormData } from "../types/index";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { cloudflareService } from "@/services/cloudflareService";
import type { Database } from '@/integrations/supabase/types';
import BusinessLogoUpload from './BusinessLogoUpload';
import LocationAccordion from './LocationAccordion';
import { LocationService } from '../services/locationService';
import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { IndustrySelector } from '@/components/industries/IndustrySelector';
import { UKIndustryService } from '@/services/ukIndustryService';
import ProductsManagement from '@/components/products/ProductsManagement';
import { Package } from 'lucide-react';

type Business = Database['public']['Tables']['businesses']['Row'];

const businessFormSchema = z.object({
  business_name: z.string().min(2, "Business name must be at least 2 characters"),
  contact_email: z.string().email("Invalid email address"),
  contact_phone: z.string().optional(),
  website: z.string()
    .transform(value => {
      if (!value) return "";
      if (value.startsWith("www.")) return `https://${value}`;
      if (!value.startsWith("http")) return `https://${value}`;
      return value;
    })
    .pipe(z.string().url("Please enter a valid website URL").optional())
    .or(z.literal("")),
  linkedin: z.string().url("Invalid LinkedIn URL").optional().or(z.literal("")),
  twitter: z.string().optional(),
  address_line_1: z.string().optional(),
  address_line_2: z.string().optional(),
  city: z.string().optional(),
  postcode: z.string().optional(),
});

interface BusinessFormProps {
  onSubmit?: (data: BusinessFormData) => void;
  initialData?: Business;
  onCancel?: () => void;
  onUpgradeToSubscriptions?: () => void;
}

const BusinessForm: React.FC<BusinessFormProps> = ({ onSubmit, initialData, onCancel, onUpgradeToSubscriptions }) => {
  const { toast } = useToast();
  const [logoUrl, setLogoUrl] = useState<string | null>(initialData?.logo_url || null);
  const [logoCloudflareKey, setLogoCloudflareKey] = useState<string | null>(initialData?.logo_cloudflare_key || null);
  const [initialLogoCloudflareKey] = useState<string | null>(initialData?.logo_cloudflare_key || null);
  const [submitting, setSubmitting] = useState(false);
  
  // Location state
  const [selectedHeadquarters, setSelectedHeadquarters] = useState<Set<string>>(
    initialData?.headquarters_location_id ? new Set([initialData.headquarters_location_id]) : new Set()
  );
  const [customerLocationKeys, setCustomerLocationKeys] = useState<Set<string>>(new Set());

  // Net-zero categories state
  const [selectedNetZeroCategories, setSelectedNetZeroCategories] = useState<string[]>([]);
  const [primaryNetZeroCategory, setPrimaryNetZeroCategory] = useState<string | undefined>(undefined);

  // Industry state
  const [selectedBusinessIndustryId, setSelectedBusinessIndustryId] = useState<string | null>(null);
  const [selectedTargetIndustryIds, setSelectedTargetIndustryIds] = useState<string[]>([]);

  const form = useForm<z.infer<typeof businessFormSchema>>({
    resolver: zodResolver(businessFormSchema),
    defaultValues: initialData ? {
      business_name: initialData.business_name,
      contact_email: initialData.contact_email,
      contact_phone: initialData.contact_phone || "",
      website: initialData.website || "",
      linkedin: initialData.linkedin || "",
      twitter: initialData.twitter || "",
      address_line_1: initialData.address_line_1 || "",
      address_line_2: initialData.address_line_2 || "",
      city: initialData.city || "",
      postcode: initialData.postcode || "",
    } : {
      business_name: "",
      contact_email: "",
      contact_phone: "",
      website: "",
      linkedin: "",
      twitter: "",
      address_line_1: "",
      address_line_2: "",
      city: "",
      postcode: "",
    },
  });

  // No longer needed - using accordion components for location selection

  // Load existing business location data when editing
  useEffect(() => {
    const loadBusinessLocations = async () => {
      if (initialData?.id) {
        try {
          // Load headquarters location if it exists
          if (initialData.headquarters_location_id) {
            const headquartersKeys = await LocationService.convertIdsToSelectionKeys([initialData.headquarters_location_id]);
            if (headquartersKeys.length > 0) {
              setSelectedHeadquarters(new Set(headquartersKeys));
            }
          }

          // Load customer locations
          const businessWithLocations = await LocationService.getBusinessWithLocations(initialData.id);
          if (businessWithLocations?.customer_locations) {
            const customerLocationIds = businessWithLocations.customer_locations.map(location => location.id);
            const customerKeys = await LocationService.convertIdsToSelectionKeys(customerLocationIds);
            setCustomerLocationKeys(new Set(customerKeys));
          }
        } catch (error) {
          console.error('Failed to load business locations:', error);
        }
      }
    };

    loadBusinessLocations();
  }, [initialData?.id, initialData?.headquarters_location_id]);

  // Load existing business net-zero categories when editing
  useEffect(() => {
    const loadBusinessCategories = async () => {
      if (initialData?.id) {
        try {
          const { categories, primary_category } = await NetZeroCategoryService.getBusinessCategories(initialData.id);
          setSelectedNetZeroCategories(categories.map(cat => cat.id));
          if (primary_category) {
            setPrimaryNetZeroCategory(primary_category.id);
          }
        } catch (error) {
          console.error('Failed to load business categories:', error);
        }
      }
    };

    loadBusinessCategories();
  }, [initialData?.id]);

  // Load existing business industries when editing
  useEffect(() => {
    const loadBusinessIndustries = async () => {
      if (initialData?.id) {
        try {
          // Use the main_industry_id from the business directly
          setSelectedBusinessIndustryId(initialData.main_industry_id || null);
          
          // Load target industries from the business_target_industries table
          const { target_industries } = await UKIndustryService.getBusinessIndustries(initialData.id);
          setSelectedTargetIndustryIds(target_industries.map(industry => industry.id));
        } catch (error) {
          console.error('Failed to load business industries:', error);
        }
      }
    };

    loadBusinessIndustries();
  }, [initialData?.id, initialData?.main_industry_id]);

  const onFormSubmit = async (data: z.infer<typeof businessFormSchema>) => {
    try {
      setSubmitting(true);
      
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to manage businesses",
          variant: "destructive",
        });
        return;
      }

      // Handle logo cleanup if logo was removed or changed
      if (initialLogoCloudflareKey && initialLogoCloudflareKey !== logoCloudflareKey) {
        try {
          await cloudflareService.deleteBusinessImage(initialLogoCloudflareKey);
        } catch (error) {
          console.error('Error deleting old logo:', error);
          // Continue with save even if deletion fails
        }
      }

      if (initialData) {
        // Convert headquarters key to ID if selected
        let headquartersLocationId = null;
        if (selectedHeadquarters.size > 0) {
          const headquartersKeys = Array.from(selectedHeadquarters);
          const headquartersIds = await LocationService.convertSelectionKeysToIds(headquartersKeys);
          headquartersLocationId = headquartersIds[0] || null;
        }

        // Update existing business
        const { error } = await supabase
          .from("businesses")
          .update({
            ...data,
            logo_url: logoUrl,
            logo_cloudflare_key: logoCloudflareKey,
            headquarters_location_id: headquartersLocationId,
            main_industry_id: selectedBusinessIndustryId,
            updated_at: new Date().toISOString(),
          } as any)
          .eq('id', initialData.id)
          .eq('owner_id', user.id);

        if (error) throw error;

        // Update customer locations
        if (customerLocationKeys.size > 0) {
          console.log('Customer location keys:', Array.from(customerLocationKeys));
          const customerLocationIds = await LocationService.convertSelectionKeysToIds(Array.from(customerLocationKeys));
          console.log('Converted customer location IDs:', customerLocationIds);
          if (customerLocationIds.length > 0) {
            await LocationService.setBusinessCustomerLocations(initialData.id, customerLocationIds);
            console.log('Successfully updated business customer locations');
          } else {
            console.log('No valid customer location IDs found, skipping update');
          }
        } else {
          console.log('No customer locations selected, clearing existing locations');
          await LocationService.setBusinessCustomerLocations(initialData.id, []);
        }

        // Update net-zero categories
        await NetZeroCategoryService.updateBusinessCategories(
          initialData.id,
          selectedNetZeroCategories,
          primaryNetZeroCategory
        );

        // Update business target industries (only target industries, main industry is saved above)
        await UKIndustryService.updateBusinessTargetIndustries(initialData.id, selectedTargetIndustryIds);

        toast({
          title: "Success",
          description: "Business updated successfully",
        });
      } else {
        // Convert headquarters key to ID if selected
        let headquartersLocationId = null;
        if (selectedHeadquarters.size > 0) {
          const headquartersKeys = Array.from(selectedHeadquarters);
          const headquartersIds = await LocationService.convertSelectionKeysToIds(headquartersKeys);
          headquartersLocationId = headquartersIds[0] || null;
        }

        // Create new business
        const { data: newBusiness, error } = await supabase
          .from("businesses")
          .insert({
            ...data,
            logo_url: logoUrl,
            logo_cloudflare_key: logoCloudflareKey,
            headquarters_location_id: headquartersLocationId,
            main_industry_id: selectedBusinessIndustryId,
            owner_id: user.id,
          } as any)
          .select()
          .single();

        if (error) throw error;

        // Add customer locations for new business
        if (customerLocationKeys.size > 0 && newBusiness) {
          console.log('Customer location keys:', Array.from(customerLocationKeys));
          const customerLocationIds = await LocationService.convertSelectionKeysToIds(Array.from(customerLocationKeys));
          console.log('Converted customer location IDs:', customerLocationIds);
          if (customerLocationIds.length > 0) {
            await LocationService.setBusinessCustomerLocations(newBusiness.id, customerLocationIds);
            console.log('Successfully set business customer locations');
          } else {
            console.log('No valid customer location IDs found, skipping');
          }
        } else {
          console.log('No customer locations selected or business not created');
        }

        // Add net-zero categories for new business
        if (newBusiness && selectedNetZeroCategories.length > 0) {
          await NetZeroCategoryService.updateBusinessCategories(
            newBusiness.id,
            selectedNetZeroCategories,
            primaryNetZeroCategory
          );
        }

        // Add target industries for new business (only target industries, main industry is saved above)
        if (newBusiness && selectedTargetIndustryIds.length > 0) {
          await UKIndustryService.updateBusinessTargetIndustries(newBusiness.id, selectedTargetIndustryIds);
        }

        toast({
          title: "Success",
          description: "Business added successfully",
        });
      }

      if (onSubmit) {
        // Convert headquarters key to ID for the callback
        let headquartersLocationIdForCallback = null;
        if (selectedHeadquarters.size > 0) {
          const headquartersKeys = Array.from(selectedHeadquarters);
          const headquartersIds = await LocationService.convertSelectionKeysToIds(headquartersKeys);
          headquartersLocationIdForCallback = headquartersIds[0] || null;
        }

        const formDataWithLocations = {
          ...data,
          headquarters_location_id: headquartersLocationIdForCallback,
          customer_location_keys: Array.from(customerLocationKeys)
        } as BusinessFormData;
        onSubmit(formDataWithLocations);
      }
      if (!initialData) {
        form.reset(); // Only reset if we're creating a new business
        setLogoUrl(null);
        setLogoCloudflareKey(null);
        setSelectedHeadquarters(new Set());
        setCustomerLocationKeys(new Set());
      }
    } catch (error) {
      console.error("Error managing business:", error);
      toast({
        title: "Error",
        description: `Failed to ${initialData ? 'update' : 'add'} business. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleLogoUpdate = (url: string | null, key: string | null) => {
    setLogoUrl(url);
    setLogoCloudflareKey(key);
  };

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Logo Upload Section */}
          <div className="space-y-2">
            <BusinessLogoUpload
              currentLogoUrl={logoUrl}
              currentLogoCloudflareKey={logoCloudflareKey}
              onLogoUpdate={handleLogoUpdate}
              businessId={initialData?.id || 'temp-id'}
              businessName={form.watch('business_name')}
            />
          </div>

        <FormField
          control={form.control}
          name="business_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Business Name</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter your business name" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contact_email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact Email</FormLabel>
              <FormControl>
                <Input {...field} type="email" placeholder="<EMAIL>" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contact_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact Phone</FormLabel>
              <FormControl>
                <Input {...field} type="tel" placeholder="+44 1234 567890" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input {...field} type="url" placeholder="https://example.com" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="linkedin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>LinkedIn</FormLabel>
                <FormControl>
                  <Input {...field} type="url" placeholder="https://linkedin.com/company/..." />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="twitter"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Twitter Handle</FormLabel>
              <FormControl>
                <Input {...field} placeholder="@username" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <FormField
            control={form.control}
            name="address_line_1"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address Line 1</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Street address" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address_line_2"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address Line 2</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Apartment, suite, etc." />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="City" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postcode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Postcode</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Postcode" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Separator />

        {/* Location Section */}
        <div className="space-y-6">

          {/* Headquarters Location */}
          <div className="space-y-4">
            <div className="border rounded-lg">
              <LocationAccordion
                selectedItems={selectedHeadquarters}
                onSelectionChange={setSelectedHeadquarters}
                title="Headquarters Location"
                subtitle="Select your main business headquarters location"
                allowMultiple={false}
              />
            </div>
          </div>

          {/* Customer Locations */}
          <div className="space-y-4">
            <div className="border rounded-lg">
              <LocationAccordion
                selectedItems={customerLocationKeys}
                onSelectionChange={setCustomerLocationKeys}
                title="Customer Regions"
                subtitle="Select all regions where your business has customers or provides services"
                allowMultiple={true}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Net-Zero Categories Section */}
        <div className="space-y-4">
          <NetZeroCategorySelector
            selectedSubcategories={selectedNetZeroCategories}
            onSelectionChange={setSelectedNetZeroCategories}
            primarySubcategoryId={primaryNetZeroCategory}
            onPrimaryChange={setPrimaryNetZeroCategory}
            allowPrimarySelection={true}
            title="Business Net-Zero Categories"
            description="Select the net-zero categories that best describe your business focus. Choose one as your primary category."
            maxSelections={5}
          />
        </div>

        <Separator />

        {/* Industry Selection Section */}
        <div className="space-y-6">
          {/* Main Business Industry */}
          <div className="space-y-4">
            <IndustrySelector
              selectedIndustryId={selectedBusinessIndustryId}
              onIndustryChange={setSelectedBusinessIndustryId}
              mode="single"
              title="Main Business Industry"
              description="Select the primary industry your business operates in. This will be displayed as your main industry classification."
              singleSelectLabel="Primary Industry"
              allowParentSelection={false}
            />
          </div>

          {/* Target Industries */}
          <div className="space-y-4">
            <IndustrySelector
              selectedTargetIndustryIds={selectedTargetIndustryIds}
              onTargetIndustriesChange={setSelectedTargetIndustryIds}
              mode="multi"
              title="Target Industries"
              description="Select the industries you serve, target as customers, or work with. You can select multiple industries."
              multiSelectLabel="Industries You Serve"
              maxSelections={8}
              allowParentSelection={false}
            />
          </div>
        </div>

        <Separator />

        {/* Products Section - Show for both new and existing businesses */}
        <div className="space-y-4">
          <div className="border rounded-lg p-6">
            {initialData?.id ? (
              <ProductsManagement
                businessId={initialData.id}
                isOwner={true}
                title="Products & Services"
                description="Add and manage your products with their carbon impact tracking"
                onUpgradeToSubscriptions={onUpgradeToSubscriptions}
              />
            ) : (
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Products & Services</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    You can add products and their carbon impact tracking after creating your business
                  </p>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Package className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">Coming Next</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        After saving your business, you'll be able to add products and services with their carbon footprint metrics to showcase your sustainability efforts.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <Separator />

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" className="flex-1 max-w-xs" disabled={submitting || form.formState.isSubmitting}>
            {(submitting || form.formState.isSubmitting)
              ? (initialData ? "Updating..." : "Adding...") 
              : (initialData ? "Update Business" : "Add Business")}
          </Button>
        </div>
        </form>
      </Form>
    </div>
  );
};

export default BusinessForm;
