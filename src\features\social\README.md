# Social Feed & Connections Feature

The social feed feature provides a community-driven platform for users to share insights, connect with others, and engage in discussions about sustainability and net-zero initiatives.

## Overview

This feature implements a Twitter/X-like social platform with:
- **Posts**: Text-based content with optional industry and net-zero category tags
- **Comments & Replies**: 4-level nested discussion threads (post → comment → reply → reply to reply)
- **Connections**: Follow/unfollow system for curated content feeds
- **Filtering**: View all posts or filter by followed users only
- **Categories**: Tag posts with industry and net-zero categories for better discovery
- **Reactions**: Four reaction types (Like, Support, Love, Insightful) with real-time counts

## Architecture

### Database Schema

#### Core Tables
1. **social_posts** - Main posts table with reaction counts
2. **social_comments** - Comments and replies (self-referencing for nesting)
3. **social_follows** - User follow relationships
4. **social_post_reactions** - User reactions to posts (Like, Support, Love, Insightful)
5. **social_post_categories** - Junction table for post net-zero categories
6. **social_post_industries** - Junction table for post industry tags

#### Key Relationships
- Posts belong to users (profiles)
- Comments reference posts and can reference parent comments
- Follows create many-to-many user relationships
- Posts can have multiple categories and industries

### Component Structure

```
src/features/social/
├── README.md
├── index.ts
├── components/
│   ├── SocialPost.tsx           # Individual post display
│   ├── PostCreator.tsx          # Post creation form
│   ├── PostReactions.tsx        # Post reaction buttons and counts
│   ├── CommentThread.tsx        # Nested comment display
│   ├── CommentForm.tsx          # Comment/reply form
│   ├── FollowButton.tsx         # Follow/unfollow button
│   ├── SocialFilters.tsx        # Feed filtering controls
│   └── UserConnectionsList.tsx  # Following/followers lists
├── pages/
│   ├── SocialFeedPage.tsx       # Main social feed
│   └── UserSocialProfile.tsx    # User's social profile
├── services/
│   ├── socialService.ts         # API service layer
│   └── socialNotifications.ts   # Social-specific notifications
├── hooks/
│   ├── useSocialFeed.ts         # Feed data management
│   ├── useComments.ts           # Comment thread management
│   └── useConnections.ts        # Follow/unfollow logic
└── types/
    └── social.types.ts          # TypeScript definitions
```

### Integration Points

#### Existing Systems
- **Authentication**: Uses existing AuthContext and user profiles
- **Categories**: Leverages existing net-zero categories and UK industries
- **Notifications**: Extends existing notification system for social interactions
- **Navigation**: Integrates with existing Navbar and routing

#### Data Flow
1. Posts created with optional category/industry tags
2. Real-time updates via Supabase subscriptions
3. Filtered feeds based on user connections
4. Notifications for interactions (comments, follows)

## Features

### Core Functionality
- ✅ Create text posts with category tags
- ✅ 4-level nested comment system
- ✅ Follow/unfollow users
- ✅ Filter feed by connections
- ✅ Edit own posts and comments
- ✅ Real-time updates

### Content Management
- **Post Creation**: Rich text editor with category selection
- **Comment Threading**: Visual nesting with reply chains
- **Content Moderation**: User can edit/delete own content
- **Category Tagging**: Industry and net-zero category selection

### Social Features
- **Connections**: Simple follow model (no mutual approval needed)
- **Feed Filtering**: Toggle between "All Posts" and "Following"
- **User Discovery**: Find users through posts and member directory
- **Activity Notifications**: Alerts for comments and new followers

## Technical Implementation

### Database Design
- Optimized for read performance with proper indexing
- Soft deletes for content moderation
- Efficient nested comment queries
- Real-time subscription support

### Performance Considerations
- Infinite scroll pagination
- Optimistic UI updates
- Cached user data
- Efficient comment tree rendering

### Security & Privacy
- Row Level Security (RLS) policies
- User content ownership validation
- Privacy-respecting follow system
- GDPR-compliant data handling

## Development Phases

### Phase 1: Core Infrastructure
1. Database schema and migrations
2. Basic service layer
3. Core TypeScript types

### Phase 2: Basic Social Features
1. Post creation and display
2. Simple comment system
3. Follow/unfollow functionality

### Phase 3: Enhanced UX
1. Nested comment threading
2. Real-time updates
3. Feed filtering and sorting

### Phase 4: Polish & Integration
1. Notifications integration
2. Navigation updates
3. Testing and documentation

## Future Enhancements

### Potential Features
- Post reactions (like, helpful, etc.)
- Image/file attachments
- Hashtag system
- Advanced search and filtering
- User mentions and tagging
- Post bookmarking/saving
- Content reporting system
- Analytics and insights

### Scalability Considerations
- Content archiving strategies
- Advanced caching layers
- Search optimization
- Mobile app compatibility
