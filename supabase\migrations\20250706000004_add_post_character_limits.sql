-- Add character limit validation based on user subscription
-- This migration adds a function to check character limits based on subscription status

-- Create function to check if user has premium subscription
CREATE OR REPLACE FUNCTION public.user_has_premium_subscription(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user has active business_premium or advertising subscription
    RETURN EXISTS (
        SELECT 1 
        FROM public.user_subscriptions us
        JOIN public.subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = user_has_premium_subscription.user_id
        AND us.status = 'active'
        AND sp.plan_type IN ('business_premium', 'advertising', 'advertising_large', 'advertising_medium', 'advertising_small')
        AND (us.current_period_end IS NULL OR us.current_period_end > NOW())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to validate post character limits
CREATE OR REPLACE FUNCTION public.validate_post_character_limit()
RETURNS TRIGGER AS $$
DECLARE
    is_premium BOOLEAN;
    max_length INTEGER;
BEGIN
    -- Get user's subscription status
    is_premium := public.user_has_premium_subscription(NEW.user_id);
    
    -- Set character limit based on subscription
    IF is_premium THEN
        max_length := 2000;
    ELSE
        max_length := 280;
    END IF;
    
    -- Check if content exceeds limit
    IF char_length(NEW.content) > max_length THEN
        RAISE EXCEPTION 'Post content exceeds character limit. Maximum allowed: % characters (Premium users: 2000, Free users: 280)', max_length;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to validate character limits on insert and update
CREATE TRIGGER trigger_validate_post_character_limit
    BEFORE INSERT OR UPDATE ON public.social_posts
    FOR EACH ROW EXECUTE FUNCTION public.validate_post_character_limit();

-- Update the existing constraint to allow up to 2000 characters (will be limited by trigger)
ALTER TABLE public.social_posts 
DROP CONSTRAINT IF EXISTS social_posts_content_check;

ALTER TABLE public.social_posts 
ADD CONSTRAINT social_posts_content_check 
CHECK (char_length(content) >= 1 AND char_length(content) <= 2000);

-- Grant execute permissions on the function
GRANT EXECUTE ON FUNCTION public.user_has_premium_subscription(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.user_has_premium_subscription(UUID) TO service_role;

-- Add comments for documentation
COMMENT ON FUNCTION public.user_has_premium_subscription(UUID) IS 'Check if user has active premium subscription (business_premium or advertising)';
COMMENT ON FUNCTION public.validate_post_character_limit() IS 'Validate post character limits based on user subscription: 280 for free users, 2000 for premium users';
