import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { X, Plus, Loader2, Send } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { PostFormData } from '../types/social.types';

interface PostCreatorProps {
  onPostCreated: (postData: PostFormData) => Promise<void>;
  className?: string;
}

interface NetZeroCategory {
  id: string;
  name: string;
  category: {
    name: string;
  };
}

interface Industry {
  id: string;
  name: string;
  parent_name?: string;
}

const PostCreator: React.FC<PostCreatorProps> = ({ onPostCreated, className = '' }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(false);
  const [content, setContent] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<NetZeroCategory[]>([]);
  const [industries, setIndustries] = useState<Industry[]>([]);
  const [loadingData, setLoadingData] = useState(false);

  // Load categories and industries when component expands
  useEffect(() => {
    if (isExpanded && categories.length === 0 && industries.length === 0) {
      loadCategoriesAndIndustries();
    }
  }, [isExpanded, categories.length, industries.length]);

  const loadCategoriesAndIndustries = async () => {
    setLoadingData(true);
    try {
      // Load net-zero categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('netzero_subcategories')
        .select(`
          id,
          name,
          category:netzero_categories (
            name
          )
        `)
        .order('name');

      if (categoriesError) {
        console.error('Error loading categories:', categoriesError);
      } else {
        setCategories(categoriesData || []);
      }

      // Load industries using the new view
      const { data: industriesData, error: industriesError } = await supabase
        .from('uk_industries_with_parent')
        .select('id, name, parent_name')
        .order('name');

      if (industriesError) {
        console.error('Error loading industries:', industriesError);
      } else {
        setIndustries(industriesData || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      toast({
        title: "Content required",
        description: "Please write something before posting.",
        variant: "destructive"
      });
      return;
    }

    if (content.length > 2000) {
      toast({
        title: "Post too long",
        description: "Posts must be 2000 characters or less.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      const postData: PostFormData = {
        content: content.trim(),
        netZeroCategoryIds: selectedCategories,
        industryIds: selectedIndustries
      };

      await onPostCreated(postData);
      
      // Reset form
      setContent('');
      setSelectedCategories([]);
      setSelectedIndustries([]);
      setIsExpanded(false);
      
      toast({
        title: "Post created",
        description: "Your post has been shared with the community."
      });
    } catch (error) {
      console.error('Error creating post:', error);
      toast({
        title: "Error",
        description: "Failed to create post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    if (!selectedCategories.includes(categoryId)) {
      setSelectedCategories([...selectedCategories, categoryId]);
    }
  };

  const handleIndustrySelect = (industryId: string) => {
    if (!selectedIndustries.includes(industryId)) {
      setSelectedIndustries([...selectedIndustries, industryId]);
    }
  };

  const removeCategory = (categoryId: string) => {
    setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
  };

  const removeIndustry = (industryId: string) => {
    setSelectedIndustries(selectedIndustries.filter(id => id !== industryId));
  };

  const getSelectedCategoryNames = () => {
    return categories.filter(cat => selectedCategories.includes(cat.id));
  };

  const getSelectedIndustryNames = () => {
    return industries.filter(ind => selectedIndustries.includes(ind.id));
  };

  if (!user) {
    return null;
  }

  if (!isExpanded) {
    return (
      <Card className={`mb-6 ${className}`}>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
              <Plus size={20} />
            </div>
            <div className="flex-1">
              <Button 
                variant="outline" 
                className="w-full justify-start text-muted-foreground"
                onClick={() => setIsExpanded(true)}
              >
                What's on your mind about sustainability?
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`mb-6 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">Create a Post</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Content Input */}
          <div>
            <Textarea
              placeholder="Share your sustainability insights, ask questions, or start a discussion..."
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="min-h-[120px] resize-none"
              maxLength={2000}
              autoFocus
            />
            <div className="flex justify-between items-center mt-2">
              <span className="text-xs text-muted-foreground">
                {content.length}/2000 characters
              </span>
            </div>
          </div>

          {/* Category Selection */}
          <div>
            <Label className="text-sm font-medium">Net-Zero Categories (optional)</Label>
            <Select onValueChange={handleCategorySelect} disabled={loadingData}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Add relevant categories..." />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem 
                    key={category.id} 
                    value={category.id}
                    disabled={selectedCategories.includes(category.id)}
                  >
                    {category.category.name}: {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Selected Categories */}
            {selectedCategories.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {getSelectedCategoryNames().map(category => (
                  <Badge key={category.id} variant="secondary" className="text-xs">
                    {category.category.name}: {category.name}
                    <button
                      type="button"
                      onClick={() => removeCategory(category.id)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Industry Selection */}
          <div>
            <Label className="text-sm font-medium">Industries (optional)</Label>
            <Select onValueChange={handleIndustrySelect} disabled={loadingData}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Add relevant industries..." />
              </SelectTrigger>
              <SelectContent>
                {industries.map(industry => (
                  <SelectItem 
                    key={industry.id} 
                    value={industry.id}
                    disabled={selectedIndustries.includes(industry.id)}
                  >
                    {industry.parent_name ? `${industry.parent_name}: ` : ''}{industry.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Selected Industries */}
            {selectedIndustries.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {getSelectedIndustryNames().map(industry => (
                  <Badge key={industry.id} variant="outline" className="text-xs">
                    {industry.parent_name ? `${industry.parent_name}: ` : ''}{industry.name}
                    <button
                      type="button"
                      onClick={() => removeIndustry(industry.id)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                setIsExpanded(false);
                setContent('');
                setSelectedCategories([]);
                setSelectedIndustries([]);
              }}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              disabled={!content.trim() || isSubmitting || loadingData}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Posting...
                </>
              ) : (
                <>
                  <Send size={16} className="mr-2" />
                  Post
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default PostCreator;
