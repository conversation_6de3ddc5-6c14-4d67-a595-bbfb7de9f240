import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, ThumbsUp, Lightbulb, HandHeart, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { SocialService } from '../services/socialService';
import type { ReactionType, SocialPostWithAuthor } from '../types/social.types';

interface PostReactionsProps {
  post: SocialPostWithAuthor;
  onReactionChange?: (postId: string) => void;
  variant?: 'default' | 'compact';
  className?: string;
}

const reactionConfig = {
  like: {
    icon: ThumbsUp,
    label: 'Like',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
    activeColor: 'bg-blue-100 text-blue-700',
    count: (post: SocialPostWithAuthor) => post.like_count || 0
  },
  support: {
    icon: HandHeart,
    label: 'Support',
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100',
    activeColor: 'bg-green-100 text-green-700',
    count: (post: SocialPostWithAuthor) => post.support_count || 0
  },
  love: {
    icon: Heart,
    label: 'Love',
    color: 'text-red-600',
    bgColor: 'bg-red-50 hover:bg-red-100',
    activeColor: 'bg-red-100 text-red-700',
    count: (post: SocialPostWithAuthor) => post.love_count || 0
  },
  insightful: {
    icon: Lightbulb,
    label: 'Insightful',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100',
    activeColor: 'bg-yellow-100 text-yellow-700',
    count: (post: SocialPostWithAuthor) => post.insightful_count || 0
  }
};

const PostReactions: React.FC<PostReactionsProps> = ({
  post,
  onReactionChange,
  variant = 'default',
  className = ''
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loadingReaction, setLoadingReaction] = useState<ReactionType | null>(null);
  const [optimisticReactions, setOptimisticReactions] = useState<ReactionType[]>(
    post.user_reactions || []
  );

  const handleReactionClick = async (reactionType: ReactionType) => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to react to posts.",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoadingReaction(reactionType);
      
      // Optimistic update
      const wasActive = optimisticReactions.includes(reactionType);
      const newReactions = wasActive
        ? optimisticReactions.filter(r => r !== reactionType)
        : [...optimisticReactions, reactionType];
      
      setOptimisticReactions(newReactions);

      // Make API call
      const isNowActive = await SocialService.toggleReaction(post.id, reactionType);
      
      // Update with actual result (in case optimistic update was wrong)
      if (isNowActive !== !wasActive) {
        setOptimisticReactions(
          isNowActive
            ? [...optimisticReactions.filter(r => r !== reactionType), reactionType]
            : optimisticReactions.filter(r => r !== reactionType)
        );
      }

      // Notify parent component to refresh data
      onReactionChange?.(post.id);

    } catch (error) {
      console.error('Error toggling reaction:', error);
      
      // Revert optimistic update on error
      setOptimisticReactions(post.user_reactions || []);
      
      toast({
        title: "Error",
        description: "Failed to update reaction. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingReaction(null);
    }
  };

  const isReactionActive = (reactionType: ReactionType) => {
    return optimisticReactions.includes(reactionType);
  };

  const getReactionCount = (reactionType: ReactionType) => {
    const baseCount = reactionConfig[reactionType].count(post);
    const wasOriginallyActive = (post.user_reactions || []).includes(reactionType);
    const isCurrentlyActive = optimisticReactions.includes(reactionType);
    
    if (wasOriginallyActive && !isCurrentlyActive) {
      return Math.max(0, baseCount - 1);
    } else if (!wasOriginallyActive && isCurrentlyActive) {
      return baseCount + 1;
    }
    return baseCount;
  };

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        {(Object.keys(reactionConfig) as ReactionType[]).map(reactionType => {
          const config = reactionConfig[reactionType];
          const Icon = config.icon;
          const count = getReactionCount(reactionType);
          const isActive = isReactionActive(reactionType);
          const isLoading = loadingReaction === reactionType;

          if (count === 0 && !isActive) return null;

          return (
            <Button
              key={reactionType}
              variant="ghost"
              size="sm"
              className={`h-6 px-2 text-xs ${
                isActive ? config.activeColor : config.color
              }`}
              onClick={() => handleReactionClick(reactionType)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 size={12} className="animate-spin" />
              ) : (
                <Icon size={12} className="mr-1" />
              )}
              {count}
            </Button>
          );
        })}
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {(Object.keys(reactionConfig) as ReactionType[]).map(reactionType => {
        const config = reactionConfig[reactionType];
        const Icon = config.icon;
        const count = getReactionCount(reactionType);
        const isActive = isReactionActive(reactionType);
        const isLoading = loadingReaction === reactionType;

        return (
          <Button
            key={reactionType}
            variant="ghost"
            size="sm"
            className={`h-8 px-3 text-sm transition-colors ${
              isActive 
                ? config.activeColor 
                : `${config.color} ${config.bgColor}`
            }`}
            onClick={() => handleReactionClick(reactionType)}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 size={16} className="mr-1 animate-spin" />
            ) : (
              <Icon size={16} className="mr-1" />
            )}
            {config.label}
            {count > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                {count}
              </Badge>
            )}
          </Button>
        );
      })}
      
      {/* Total reactions summary */}
      {post.total_reaction_count > 0 && (
        <span className="text-xs text-muted-foreground ml-2">
          {post.total_reaction_count} reaction{post.total_reaction_count !== 1 ? 's' : ''}
        </span>
      )}
    </div>
  );
};

export default PostReactions;
