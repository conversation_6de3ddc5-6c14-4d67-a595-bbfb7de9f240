-- Create Social Feed & Connections System
-- This migration creates tables for social posts, comments, follows, and related functionality

-- Create social posts table
CREATE TABLE IF NOT EXISTS public.social_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL CHECK (char_length(content) >= 1 AND char_length(content) <= 2000),
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    
    -- Engagement metrics (for future use)
    comment_count INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT social_posts_content_not_empty CHECK (trim(content) != '')
);

-- Create social comments table (handles both comments and replies)
CREATE TABLE IF NOT EXISTS public.social_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.social_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES public.social_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL CHECK (char_length(content) >= 1 AND char_length(content) <= 1000),
    
    -- Hierarchy tracking
    depth INTEGER DEFAULT 0 CHECK (depth >= 0 AND depth <= 3), -- Max 4 levels: 0=comment, 1=reply, 2=reply to reply, 3=reply to reply to reply
    thread_path TEXT, -- For efficient tree queries (e.g., "comment_id.reply_id.reply_id")
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    
    -- Constraints
    CONSTRAINT social_comments_content_not_empty CHECK (trim(content) != ''),
    CONSTRAINT social_comments_max_depth CHECK (depth <= 3)
);

-- Create social follows table (user connections)
CREATE TABLE IF NOT EXISTS public.social_follows (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    follower_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    following_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(follower_id, following_id),
    CONSTRAINT social_follows_no_self_follow CHECK (follower_id != following_id)
);

-- Create junction table for post net-zero categories
CREATE TABLE IF NOT EXISTS public.social_post_netzero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.social_posts(id) ON DELETE CASCADE,
    subcategory_id UUID NOT NULL REFERENCES public.netzero_subcategories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, subcategory_id)
);

-- Create junction table for post industry tags
CREATE TABLE IF NOT EXISTS public.social_post_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.social_posts(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, industry_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_social_posts_user_id ON public.social_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_social_posts_created_at ON public.social_posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_social_posts_not_deleted ON public.social_posts(is_deleted) WHERE is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_social_comments_post_id ON public.social_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_social_comments_user_id ON public.social_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_social_comments_parent_id ON public.social_comments(parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_social_comments_created_at ON public.social_comments(created_at);
CREATE INDEX IF NOT EXISTS idx_social_comments_thread_path ON public.social_comments(thread_path);
CREATE INDEX IF NOT EXISTS idx_social_comments_not_deleted ON public.social_comments(is_deleted) WHERE is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_social_follows_follower_id ON public.social_follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_social_follows_following_id ON public.social_follows(following_id);

CREATE INDEX IF NOT EXISTS idx_social_post_netzero_categories_post_id ON public.social_post_netzero_categories(post_id);
CREATE INDEX IF NOT EXISTS idx_social_post_netzero_categories_subcategory_id ON public.social_post_netzero_categories(subcategory_id);

CREATE INDEX IF NOT EXISTS idx_social_post_industries_post_id ON public.social_post_industries(post_id);
CREATE INDEX IF NOT EXISTS idx_social_post_industries_industry_id ON public.social_post_industries(industry_id);

-- Create functions for maintaining comment counts and thread paths
CREATE OR REPLACE FUNCTION update_post_comment_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.is_deleted = false THEN
        UPDATE public.social_posts 
        SET comment_count = comment_count + 1 
        WHERE id = NEW.post_id;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.is_deleted = false AND NEW.is_deleted = true THEN
            -- Comment was deleted
            UPDATE public.social_posts 
            SET comment_count = comment_count - 1 
            WHERE id = NEW.post_id;
        ELSIF OLD.is_deleted = true AND NEW.is_deleted = false THEN
            -- Comment was restored
            UPDATE public.social_posts 
            SET comment_count = comment_count + 1 
            WHERE id = NEW.post_id;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.social_posts 
        SET comment_count = comment_count - 1 
        WHERE id = OLD.post_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create function to set thread path for comments
CREATE OR REPLACE FUNCTION set_comment_thread_path()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_comment_id IS NULL THEN
        -- Top-level comment
        NEW.depth := 0;
        NEW.thread_path := NEW.id::text;
    ELSE
        -- Reply to another comment
        SELECT depth + 1, thread_path || '.' || NEW.id::text
        INTO NEW.depth, NEW.thread_path
        FROM public.social_comments
        WHERE id = NEW.parent_comment_id;
        
        -- Ensure we don't exceed max depth
        IF NEW.depth > 3 THEN
            RAISE EXCEPTION 'Maximum comment depth exceeded';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_update_post_comment_count
    AFTER INSERT OR UPDATE OR DELETE ON public.social_comments
    FOR EACH ROW EXECUTE FUNCTION update_post_comment_count();

CREATE TRIGGER trigger_set_comment_thread_path
    BEFORE INSERT ON public.social_comments
    FOR EACH ROW EXECUTE FUNCTION set_comment_thread_path();

-- Create trigger for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_social_posts_updated_at
    BEFORE UPDATE ON public.social_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_social_comments_updated_at
    BEFORE UPDATE ON public.social_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.social_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_post_netzero_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_post_industries ENABLE ROW LEVEL SECURITY;
