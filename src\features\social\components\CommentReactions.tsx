import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Heart, ThumbsUp, Lightbulb, HandHeart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import type { ReactionType } from '../types/social.types';

interface CommentReactionsProps {
  commentId: string;
  className?: string;
}

const reactionConfig = {
  like: {
    icon: ThumbsUp,
    label: 'Like',
    color: 'text-blue-600 hover:text-blue-700',
  },
  support: {
    icon: HandHeart,
    label: 'Support',
    color: 'text-green-600 hover:text-green-700',
  },
  love: {
    icon: Heart,
    label: 'Love',
    color: 'text-red-600 hover:text-red-700',
  },
  insightful: {
    icon: Lightbulb,
    label: 'Insightful',
    color: 'text-yellow-600 hover:text-yellow-700',
  }
};

const CommentReactions: React.FC<CommentReactionsProps> = ({
  commentId,
  className = ''
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [userReactions, setUserReactions] = useState<ReactionType[]>([]);

  const handleReactionClick = async (reactionType: ReactionType) => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to react to comments.",
        variant: "destructive"
      });
      return;
    }

    // For now, just show a placeholder - comment reactions can be implemented later
    toast({
      title: "Coming soon",
      description: "Comment reactions will be available in a future update.",
    });
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {(Object.keys(reactionConfig) as ReactionType[]).map(reactionType => {
        const config = reactionConfig[reactionType];
        const Icon = config.icon;
        const isActive = userReactions.includes(reactionType);

        return (
          <Button
            key={reactionType}
            variant="ghost"
            size="sm"
            className={`h-6 px-1.5 text-xs transition-colors ${
              isActive 
                ? 'bg-muted text-primary' 
                : `text-muted-foreground ${config.color}`
            }`}
            onClick={() => handleReactionClick(reactionType)}
            title={config.label}
          >
            <Icon size={12} />
          </Button>
        );
      })}
    </div>
  );
};

export default CommentReactions;
