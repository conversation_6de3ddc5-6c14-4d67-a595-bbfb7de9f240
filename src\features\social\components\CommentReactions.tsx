import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Heart, ThumbsUp, Lightbulb, HandHeart, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { SocialService } from '../services/socialService';
import type { ReactionType, SocialCommentWithAuthor } from '../types/social.types';

interface CommentReactionsProps {
  comment: SocialCommentWithAuthor;
  onReactionChange?: (commentId: string) => void;
  className?: string;
}

const reactionConfig = {
  like: {
    icon: ThumbsUp,
    label: 'Like',
    color: 'text-muted-foreground hover:text-blue-600',
    activeColor: 'text-blue-600 bg-blue-50',
    count: (comment: SocialCommentWithAuthor) => comment.like_count || 0
  },
  support: {
    icon: HandHeart,
    label: 'Support',
    color: 'text-muted-foreground hover:text-green-600',
    activeColor: 'text-green-600 bg-green-50',
    count: (comment: SocialCommentWithAuthor) => comment.support_count || 0
  },
  love: {
    icon: Heart,
    label: 'Love',
    color: 'text-muted-foreground hover:text-red-600',
    activeColor: 'text-red-600 bg-red-50',
    count: (comment: SocialCommentWithAuthor) => comment.love_count || 0
  },
  insightful: {
    icon: Lightbulb,
    label: 'Insightful',
    color: 'text-muted-foreground hover:text-yellow-600',
    activeColor: 'text-yellow-600 bg-yellow-50',
    count: (comment: SocialCommentWithAuthor) => comment.insightful_count || 0
  }
};

const CommentReactions: React.FC<CommentReactionsProps> = ({
  comment,
  onReactionChange,
  className = ''
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loadingReaction, setLoadingReaction] = useState<ReactionType | null>(null);
  const [optimisticReactions, setOptimisticReactions] = useState<ReactionType[]>(
    comment.user_reactions || []
  );

  useEffect(() => {
    setOptimisticReactions(comment.user_reactions || []);
  }, [comment.user_reactions]);

  const handleReactionClick = async (reactionType: ReactionType) => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to react to comments.",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoadingReaction(reactionType);

      // Optimistic update
      const wasActive = optimisticReactions.includes(reactionType);
      const newReactions = wasActive
        ? optimisticReactions.filter(r => r !== reactionType)
        : [...optimisticReactions, reactionType];

      setOptimisticReactions(newReactions);

      // Make API call
      const isNowActive = await SocialService.toggleCommentReaction(comment.id, reactionType);

      // Update with actual result (in case optimistic update was wrong)
      if (isNowActive !== !wasActive) {
        setOptimisticReactions(
          isNowActive
            ? [...optimisticReactions.filter(r => r !== reactionType), reactionType]
            : optimisticReactions.filter(r => r !== reactionType)
        );
      }

      // Notify parent component to refresh data
      onReactionChange?.(comment.id);

    } catch (error) {
      console.error('Error toggling comment reaction:', error);

      // Revert optimistic update on error
      setOptimisticReactions(comment.user_reactions || []);

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update reaction. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingReaction(null);
    }
  };

  const isReactionActive = (reactionType: ReactionType) => {
    return optimisticReactions.includes(reactionType);
  };

  const getReactionCount = (reactionType: ReactionType) => {
    const baseCount = reactionConfig[reactionType].count(comment);
    const wasOriginallyActive = (comment.user_reactions || []).includes(reactionType);
    const isCurrentlyActive = optimisticReactions.includes(reactionType);

    if (wasOriginallyActive && !isCurrentlyActive) {
      return Math.max(0, baseCount - 1);
    } else if (!wasOriginallyActive && isCurrentlyActive) {
      return baseCount + 1;
    }
    return baseCount;
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {(Object.keys(reactionConfig) as ReactionType[]).map(reactionType => {
        const config = reactionConfig[reactionType];
        const Icon = config.icon;
        const count = getReactionCount(reactionType);
        const isActive = isReactionActive(reactionType);
        const isLoading = loadingReaction === reactionType;

        return (
          <Button
            key={reactionType}
            variant="ghost"
            size="sm"
            className={`h-6 px-1.5 text-xs transition-all duration-200 rounded-full ${
              isActive
                ? config.activeColor
                : config.color
            }`}
            onClick={() => handleReactionClick(reactionType)}
            disabled={isLoading}
            title={config.label}
          >
            {isLoading ? (
              <Loader2 size={10} className="animate-spin" />
            ) : (
              <Icon
                size={10}
                className={`${count > 0 ? "mr-1" : ""} ${isActive ? 'fill-current' : ''}`}
                fill={isActive ? 'currentColor' : 'none'}
              />
            )}
            {count > 0 && count}
          </Button>
        );
      })}
    </div>
  );
};

export default CommentReactions;
