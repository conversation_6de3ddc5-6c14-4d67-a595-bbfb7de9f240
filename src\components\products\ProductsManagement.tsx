import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Plus, Package, BarChart3, Lock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSubscription } from '@/hooks/useSubscription';
import { ProductService } from '@/services/productService';
import ProductForm from './ProductForm';
import ProductsList from './ProductsList';
import CarbonMetricForm from './CarbonMetricForm';
import type {
  ProductWithCategories,
  ProductFormData,
  CarbonMetricFormData,
  CarbonMetric
} from '@/types/products.types';

interface ProductsManagementProps {
  businessId: string;
  isOwner?: boolean;
  title?: string;
  description?: string;
  onUpgradeToSubscriptions?: () => void;
}

const ProductsManagement: React.FC<ProductsManagementProps> = ({
  businessId,
  isOwner = false,
  title = "Products & Services",
  description = "Manage your products and their carbon impact metrics",
  onUpgradeToSubscriptions
}) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { canAddProduct, isFeatureLocked, getUpgradeMessage } = useSubscription();
  const [products, setProducts] = useState<ProductWithCategories[]>([]);
  const [loading, setLoading] = useState(true);
  const [showProductForm, setShowProductForm] = useState(false);
  const [showMetricForm, setShowMetricForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ProductWithCategories | null>(null);
  const [selectedProductForMetric, setSelectedProductForMetric] = useState<ProductWithCategories | null>(null);
  const [productToDelete, setProductToDelete] = useState<ProductWithCategories | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Load products
  const loadProducts = async () => {
    try {
      setLoading(true);
      const { products: productsData } = await ProductService.getBusinessProducts(businessId);
      setProducts(productsData);
    } catch (error) {
      console.error('Failed to load products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load products',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (businessId) {
      loadProducts();
    }
  }, [businessId]);

  // Handle product form submission
  const handleProductSubmit = async (data: ProductFormData) => {
    try {
      setSubmitting(true);
      
      if (editingProduct) {
        await ProductService.updateProduct(editingProduct.id, data);
        toast({
          title: 'Success',
          description: 'Product updated successfully',
        });
      } else {
        await ProductService.createProduct(businessId, data);
        toast({
          title: 'Success',
          description: 'Product created successfully',
        });
      }

      setShowProductForm(false);
      setEditingProduct(null);
      await loadProducts();
    } catch (error) {
      console.error('Error saving product:', error);
      toast({
        title: 'Error',
        description: `Failed to ${editingProduct ? 'update' : 'create'} product`,
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle carbon metric form submission
  const handleMetricSubmit = async (data: CarbonMetricFormData) => {
    try {
      setSubmitting(true);

      // Create metric for specific product or business-wide
      const productId = selectedProductForMetric?.id;
      await ProductService.createCarbonMetric(businessId, data, productId);

      const metricType = selectedProductForMetric ? 'product-specific' : 'business-wide';
      toast({
        title: 'Success',
        description: `${metricType} carbon metric added successfully`,
      });

      setShowMetricForm(false);
      setSelectedProductForMetric(null);
      await loadProducts();
    } catch (error) {
      console.error('Error saving carbon metric:', error);
      toast({
        title: 'Error',
        description: 'Failed to add carbon metric',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle product deletion
  const handleDeleteProduct = async () => {
    if (!productToDelete) return;

    try {
      await ProductService.deleteProduct(productToDelete.id);
      toast({
        title: 'Success',
        description: 'Product deleted successfully',
      });
      setProductToDelete(null);
      await loadProducts();
    } catch (error) {
      console.error('Error deleting product:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete product',
        variant: 'destructive',
      });
    }
  };

  // Handle navigation to subscription page
  const navigateToSubscriptions = (e?: React.MouseEvent) => {
    console.log('🚀 Navigating to subscription page...');
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    if (onUpgradeToSubscriptions) {
      console.log('📋 Using callback to switch to subscriptions tab');
      onUpgradeToSubscriptions();
    } else {
      console.log('🔄 Navigating to profile subscriptions page');
      try {
        navigate('/profile?tab=subscriptions');
      } catch (error) {
        console.error('Navigation error, using window.location:', error);
        window.location.href = '/profile?tab=subscriptions';
      }
    }
  };

  // Handle navigation for toast action (without event parameter)
  const handleToastUpgrade = () => {
    console.log('🎯 Toast upgrade button clicked');
    
    if (onUpgradeToSubscriptions) {
      console.log('📋 Using callback to switch to subscriptions tab');
      onUpgradeToSubscriptions();
    } else {
      console.log('🔄 Navigating to profile subscriptions page');
      try {
        navigate('/profile?tab=subscriptions');
      } catch (error) {
        console.error('Toast navigation error, using window.location:', error);
        window.location.href = '/profile?tab=subscriptions';
      }
    }
  };

  // Handle premium feature checks
  const handleAddProductClick = () => {
    if (!canAddProduct()) {
      toast({
        title: 'Premium Feature Required',
        description: 'Products & Services require a Business Premium subscription. Click to upgrade!',
        action: (
          <Button
            size="sm"
            type="button"
            onClick={handleToastUpgrade}
            className="bg-yellow-600 hover:bg-yellow-700"
          >
            Upgrade Now
          </Button>
        ),
      });
      return;
    }
    setShowProductForm(true);
  };

  // Handle edit product
  const handleEditProduct = (product: ProductWithCategories) => {
    setEditingProduct(product);
    setShowProductForm(true);
  };

  // Handle add metric for specific product
  const handleAddMetric = (product: ProductWithCategories) => {
    setSelectedProductForMetric(product);
    setShowMetricForm(true);
  };

  // Handle add business-wide metric (not tied to a specific product)
  const handleAddBusinessMetric = () => {
    setSelectedProductForMetric(null);
    setShowMetricForm(true);
  };

  // Handle view metrics (could navigate to a detailed metrics page)
  const handleViewMetrics = (product: ProductWithCategories) => {
    // TODO: Implement navigation to metrics detail page
    toast({
      title: 'Coming Soon',
      description: 'Detailed metrics view will be available soon',
    });
  };

  // Close forms
  const handleCloseProductForm = () => {
    setShowProductForm(false);
    setEditingProduct(null);
  };

  const handleCloseMetricForm = () => {
    setShowMetricForm(false);
    setSelectedProductForMetric(null);
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center space-y-2">
            <Package className="w-8 h-8 mx-auto text-gray-400" />
            <p className="text-gray-500">Loading products...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          {description && (
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          )}
        </div>

        {isOwner && (
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAddBusinessMetric();
              }}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Add Metrics
            </Button>
            <Button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAddProductClick();
              }}
              className={isFeatureLocked('products') ? 'relative' : ''}
            >
              {isFeatureLocked('products') ? (
                <Lock className="w-4 h-4 mr-2" />
              ) : (
                <Plus className="w-4 h-4 mr-2" />
              )}
              Add Product
              {isFeatureLocked('products') && (
                <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-1 rounded">
                  Premium
                </span>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Premium Upgrade Prompt - Show when user can't add products and has no existing products */}
      {isOwner && isFeatureLocked('products') && products.length === 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Lock className="w-8 h-8 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-yellow-900 mb-2">
                  Unlock Products & Services Showcase
                </h4>
                <p className="text-yellow-800 mb-4">
                  Upgrade to Business Premium to add unlimited products and services with carbon impact tracking.
                  Showcase your sustainability efforts and attract eco-conscious customers.
                </p>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    type="button"
                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                    onClick={(e) => {
                      console.log('🎯 Upgrade to Premium button clicked in card');
                      navigateToSubscriptions(e);
                    }}
                  >
                    Upgrade to Premium
                  </Button>
                  <Button
                    size="sm"
                    type="button"
                    variant="outline"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      toast({
                        title: 'Premium Features',
                        description: 'Business Premium includes unlimited products, events, and business images',
                      });
                    }}
                  >
                    Learn More
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Products List */}
      <ProductsList
        products={products}
        onEdit={isOwner ? handleEditProduct : undefined}
        onDelete={isOwner ? setProductToDelete : undefined}
        onAddMetric={isOwner ? handleAddMetric : undefined}
        onViewMetrics={handleViewMetrics}
        isOwner={isOwner}
      />

      {/* Product Form Dialog */}
      <Dialog open={showProductForm} onOpenChange={setShowProductForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingProduct ? 'Edit Product' : 'Add New Product'}
            </DialogTitle>
          </DialogHeader>
          <div onClick={(e) => e.stopPropagation()}>
            <ProductForm
              onSubmit={handleProductSubmit}
              initialData={editingProduct || undefined}
              onCancel={handleCloseProductForm}
              isLoading={submitting}
              businessId={businessId}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Carbon Metric Form Dialog */}
      <Dialog open={showMetricForm} onOpenChange={setShowMetricForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedProductForMetric ? (
                <>
                  Add Product Carbon Metric
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    for {selectedProductForMetric.name}
                  </span>
                </>
              ) : (
                <>
                  Add Business Carbon Metric
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    business-wide metric
                  </span>
                </>
              )}
            </DialogTitle>
          </DialogHeader>
          <div onClick={(e) => e.stopPropagation()}>
            <CarbonMetricForm
              onSubmit={handleMetricSubmit}
              onCancel={handleCloseMetricForm}
              isLoading={submitting}
              productName={selectedProductForMetric?.name}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!productToDelete} onOpenChange={() => setProductToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Product</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{productToDelete?.name}"? This action cannot be undone.
              All associated carbon metrics will also be deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProduct}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ProductsManagement; 