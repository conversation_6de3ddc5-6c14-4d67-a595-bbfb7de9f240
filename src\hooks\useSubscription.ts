import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import stripeService, { UserSubscription } from '@/services/stripeService';

export interface SubscriptionLimits {
  eventsPerMonth: number;
  businessImages: number;
  productsPerBusiness: number;
  hasUnlimitedEvents: boolean;
  hasUnlimitedImages: boolean;
  hasUnlimitedProducts: boolean;
  canAccessAdvertising: boolean;
}

export interface SubscriptionUsage {
  eventsThisMonth: number;
  businessImagesUsed: number;
  productsUsed: number;
}

const FREE_PLAN_LIMITS: SubscriptionLimits = {
  eventsPerMonth: 1,
  businessImages: 3,
  productsPerBusiness: 0, // Products require premium subscription
  hasUnlimitedEvents: false,
  hasUnlimitedImages: false,
  hasUnlimitedProducts: false,
  canAccessAdvertising: false,
};

const BUSINESS_PREMIUM_LIMITS: SubscriptionLimits = {
  eventsPerMonth: -1, // Unlimited
  businessImages: -1, // Unlimited
  productsPerBusiness: -1, // Unlimited
  hasUnlimitedEvents: true,
  hasUnlimitedImages: true,
  hasUnlimitedProducts: true,
  canAccessAdvertising: false,
};

const ADVERTISING_LIMITS: SubscriptionLimits = {
  eventsPerMonth: -1, // Unlimited
  businessImages: -1, // Unlimited
  productsPerBusiness: -1, // Unlimited
  hasUnlimitedEvents: true,
  hasUnlimitedImages: true,
  hasUnlimitedProducts: true,
  canAccessAdvertising: true,
};

export const useSubscription = () => {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [limits, setLimits] = useState<SubscriptionLimits>(FREE_PLAN_LIMITS);
  const [usage, setUsage] = useState<SubscriptionUsage>({
    eventsThisMonth: 0,
    businessImagesUsed: 0,
    productsUsed: 0,
  });
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      loadSubscriptionData();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadSubscriptionData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Load current subscription
      const currentSubscription = await stripeService.getUserSubscription(user.id);
      setSubscription(currentSubscription);

      // Determine limits based on subscription
      if (currentSubscription?.status === 'active') {
        // Get the plan details to determine limits
        const hasBusinessPremium = await stripeService.hasActivePlan(user.id, 'business_premium');
        const hasAnyAdvertising = await stripeService.hasActiveAdvertisingPlan(user.id);

        if (hasAnyAdvertising) {
          setLimits(ADVERTISING_LIMITS);
        } else if (hasBusinessPremium) {
          setLimits(BUSINESS_PREMIUM_LIMITS);
        } else {
          setLimits(FREE_PLAN_LIMITS);
        }
      } else {
        setLimits(FREE_PLAN_LIMITS);
      }

      // Load usage data
      const [eventsUsage, imagesUsage, productsUsage] = await Promise.all([
        stripeService.getUsageCount(user.id, 'events_posted'),
        stripeService.getUsageCount(user.id, 'business_images'),
        stripeService.getUsageCount(user.id, 'products_added'),
      ]);

      setUsage({
        eventsThisMonth: eventsUsage,
        businessImagesUsed: imagesUsage,
        productsUsed: productsUsage,
      });

    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const canCreateEvent = (): boolean => {
    if (limits.hasUnlimitedEvents) return true;
    return usage.eventsThisMonth < limits.eventsPerMonth;
  };

  const canAddBusinessImage = (): boolean => {
    if (limits.hasUnlimitedImages) return true;
    return usage.businessImagesUsed < limits.businessImages;
  };

  const canAddProduct = (): boolean => {
    if (limits.hasUnlimitedProducts) return true;
    return usage.productsUsed < limits.productsPerBusiness;
  };

  const getRemainingEvents = (): number => {
    if (limits.hasUnlimitedEvents) return -1; // Unlimited
    return Math.max(0, limits.eventsPerMonth - usage.eventsThisMonth);
  };

  const getRemainingImages = (): number => {
    if (limits.hasUnlimitedImages) return -1; // Unlimited
    return Math.max(0, limits.businessImages - usage.businessImagesUsed);
  };

  const getRemainingProducts = (): number => {
    if (limits.hasUnlimitedProducts) return -1; // Unlimited
    return Math.max(0, limits.productsPerBusiness - usage.productsUsed);
  };

  const incrementUsage = async (featureType: 'events_posted' | 'business_images' | 'products_added') => {
    if (!user) return;

    try {
      await stripeService.incrementUsage(user.id, featureType);
      
      // Update local usage state
      setUsage(prev => ({
        ...prev,
        [featureType === 'events_posted' ? 'eventsThisMonth' : 
         featureType === 'business_images' ? 'businessImagesUsed' : 
         'productsUsed']: prev[
          featureType === 'events_posted' ? 'eventsThisMonth' : 
          featureType === 'business_images' ? 'businessImagesUsed' : 
          'productsUsed'
        ] + 1
      }));
    } catch (error) {
      console.error('Error incrementing usage:', error);
      throw error;
    }
  };

  const getSubscriptionStatus = (): 'free' | 'business_premium' | 'advertising' | 'expired' => {
    if (!subscription || subscription.status !== 'active') {
      return 'free';
    }

    // Check if subscription has expired
    if (subscription.current_period_end && new Date(subscription.current_period_end) < new Date()) {
      return 'expired';
    }

    // Determine plan type based on active subscriptions
    if (limits.canAccessAdvertising) return 'advertising';
    if (limits.hasUnlimitedEvents) return 'business_premium';
    return 'free';
  };

  const isFeatureLocked = (feature: 'events' | 'images' | 'products' | 'advertising'): boolean => {
    switch (feature) {
      case 'events':
        return !canCreateEvent();
      case 'images':
        return !canAddBusinessImage();
      case 'products':
        return !canAddProduct();
      case 'advertising':
        return !limits.canAccessAdvertising;
      default:
        return false;
    }
  };

  const getUpgradeMessage = (feature: 'events' | 'images' | 'products' | 'advertising'): string => {
    const currentStatus = getSubscriptionStatus();
    
    switch (feature) {
      case 'events':
        if (currentStatus === 'free') {
          return 'Upgrade to Business Premium for unlimited events per month';
        }
        return 'You have reached your monthly event limit';
      
      case 'images':
        if (currentStatus === 'free') {
          return 'Upgrade to Business Premium for unlimited business images';
        }
        return 'You have reached your business image limit';
      
      case 'products':
        if (currentStatus === 'free') {
          return 'Upgrade to Business Premium for unlimited products';
        }
        return 'You have reached your product limit';
      
      case 'advertising':
        return 'Upgrade to Advertising Package to access advertising features';
      
      default:
        return 'Upgrade your subscription to access this feature';
    }
  };

  return {
    subscription,
    limits,
    usage,
    loading,
    canCreateEvent,
    canAddBusinessImage,
    canAddProduct,
    getRemainingEvents,
    getRemainingImages,
    getRemainingProducts,
    incrementUsage,
    getSubscriptionStatus,
    isFeatureLocked,
    getUpgradeMessage,
    refreshSubscription: loadSubscriptionData,
  };
};
