-- Create Comment Reactions System
-- This migration adds reaction functionality to social comments

-- Create social comment reactions table
CREATE TABLE IF NOT EXISTS public.social_comment_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES public.social_comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    reaction_type public.reaction_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(comment_id, user_id, reaction_type) -- User can only have one of each reaction type per comment
);

-- Add reaction counts to social_comments table
ALTER TABLE public.social_comments 
ADD COLUMN like_count INTEGER DEFAULT 0,
ADD COLUMN support_count INTEGER DEFAULT 0,
ADD COLUMN love_count INTEGER DEFAULT 0,
ADD COLUMN insightful_count INTEGER DEFAULT 0,
ADD COLUMN total_reaction_count INTEGER DEFAULT 0;

-- <PERSON><PERSON> indexes for performance
CREATE INDEX IF NOT EXISTS idx_social_comment_reactions_comment_id ON public.social_comment_reactions(comment_id);
CREATE INDEX IF NOT EXISTS idx_social_comment_reactions_user_id ON public.social_comment_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_social_comment_reactions_type ON public.social_comment_reactions(reaction_type);
CREATE INDEX IF NOT EXISTS idx_social_comment_reactions_created_at ON public.social_comment_reactions(created_at);

-- Create function to update comment reaction counts
CREATE OR REPLACE FUNCTION update_comment_reaction_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment the specific reaction count
        UPDATE public.social_comments 
        SET 
            like_count = like_count + CASE WHEN NEW.reaction_type = 'like' THEN 1 ELSE 0 END,
            support_count = support_count + CASE WHEN NEW.reaction_type = 'support' THEN 1 ELSE 0 END,
            love_count = love_count + CASE WHEN NEW.reaction_type = 'love' THEN 1 ELSE 0 END,
            insightful_count = insightful_count + CASE WHEN NEW.reaction_type = 'insightful' THEN 1 ELSE 0 END,
            total_reaction_count = total_reaction_count + 1
        WHERE id = NEW.comment_id;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement the specific reaction count
        UPDATE public.social_comments 
        SET 
            like_count = GREATEST(0, like_count - CASE WHEN OLD.reaction_type = 'like' THEN 1 ELSE 0 END),
            support_count = GREATEST(0, support_count - CASE WHEN OLD.reaction_type = 'support' THEN 1 ELSE 0 END),
            love_count = GREATEST(0, love_count - CASE WHEN OLD.reaction_type = 'love' THEN 1 ELSE 0 END),
            insightful_count = GREATEST(0, insightful_count - CASE WHEN OLD.reaction_type = 'insightful' THEN 1 ELSE 0 END),
            total_reaction_count = GREATEST(0, total_reaction_count - 1)
        WHERE id = OLD.comment_id;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- Handle reaction type change (remove old, add new)
        UPDATE public.social_comments 
        SET 
            like_count = like_count 
                - CASE WHEN OLD.reaction_type = 'like' THEN 1 ELSE 0 END
                + CASE WHEN NEW.reaction_type = 'like' THEN 1 ELSE 0 END,
            support_count = support_count 
                - CASE WHEN OLD.reaction_type = 'support' THEN 1 ELSE 0 END
                + CASE WHEN NEW.reaction_type = 'support' THEN 1 ELSE 0 END,
            love_count = love_count 
                - CASE WHEN OLD.reaction_type = 'love' THEN 1 ELSE 0 END
                + CASE WHEN NEW.reaction_type = 'love' THEN 1 ELSE 0 END,
            insightful_count = insightful_count 
                - CASE WHEN OLD.reaction_type = 'insightful' THEN 1 ELSE 0 END
                + CASE WHEN NEW.reaction_type = 'insightful' THEN 1 ELSE 0 END
        WHERE id = NEW.comment_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for comment reaction count updates
CREATE TRIGGER trigger_update_comment_reaction_counts
    AFTER INSERT OR UPDATE OR DELETE ON public.social_comment_reactions
    FOR EACH ROW EXECUTE FUNCTION update_comment_reaction_counts();

-- Create trigger for updating timestamps
CREATE TRIGGER trigger_social_comment_reactions_updated_at
    BEFORE UPDATE ON public.social_comment_reactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.social_comment_reactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for social_comment_reactions
-- Anyone can read reactions
CREATE POLICY "social_comment_reactions_select_policy" ON public.social_comment_reactions
    FOR SELECT USING (true);

-- Authenticated users can create reactions
CREATE POLICY "social_comment_reactions_insert_policy" ON public.social_comment_reactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own reactions (change reaction type)
CREATE POLICY "social_comment_reactions_update_policy" ON public.social_comment_reactions
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Users can delete their own reactions
CREATE POLICY "social_comment_reactions_delete_policy" ON public.social_comment_reactions
    FOR DELETE USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON TABLE public.social_comment_reactions TO authenticated;
GRANT SELECT ON TABLE public.social_comment_reactions TO anon;
GRANT ALL ON TABLE public.social_comment_reactions TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.social_comment_reactions IS 'User reactions to social comments (like, support, love, insightful)';
COMMENT ON COLUMN public.social_comments.like_count IS 'Cached count of like reactions';
COMMENT ON COLUMN public.social_comments.support_count IS 'Cached count of support reactions';
COMMENT ON COLUMN public.social_comments.love_count IS 'Cached count of love reactions';
COMMENT ON COLUMN public.social_comments.insightful_count IS 'Cached count of insightful reactions';
COMMENT ON COLUMN public.social_comments.total_reaction_count IS 'Cached count of all reactions combined';
