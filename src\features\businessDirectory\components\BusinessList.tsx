import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import type { Database } from '@/types/database.types';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Pencil, Trash2, Images } from 'lucide-react';
import BusinessForm from './BusinessForm';
import BusinessImageManager from './BusinessImageManager';
import BusinessLogoDisplay from './BusinessLogoDisplay';

type Business = Database['public']['Tables']['businesses']['Row'];

interface BusinessListProps {
  userId: string;
  onUpgradeToSubscriptions?: () => void;
}

const BusinessList: React.FC<BusinessListProps> = ({ userId, onUpgradeToSubscriptions }) => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingBusiness, setEditingBusiness] = useState<Business | null>(null);
  const [managingImages, setManagingImages] = useState<Business | null>(null);
  const { toast } = useToast();

  const fetchBusinesses = async () => {
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('*')
        .eq('owner_id', userId);

      if (error) throw error;
      // Cast the data to include the new image fields
      setBusinesses((data || []) as Business[]);
    } catch (error) {
      console.error('Error fetching businesses:', error);
      toast({
        title: "Error",
        description: "Failed to load businesses",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (businessId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this business?');
    if (!confirmed) return;

    try {
      const { error } = await supabase
        .from('businesses')
        .delete()
        .eq('id', businessId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Business deleted successfully"
      });

      // Refresh the list
      fetchBusinesses();
    } catch (error) {
      console.error('Error deleting business:', error);
      toast({
        title: "Error",
        description: "Failed to delete business",
        variant: "destructive"
      });
    }
  };

  useEffect(() => {
    fetchBusinesses();
  }, [userId]);

  if (loading) {
    return <div>Loading businesses...</div>;
  }

  if (businesses.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-muted-foreground text-center">You haven't added any businesses yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="space-y-4">
        <h3 className="text-lg font-semibold mb-4">Your Businesses</h3>
        {businesses.map((business) => (
          <Card 
            key={business.id} 
            className="group cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => navigate(`/business/${business.id}`)}
          >
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex gap-4">
                  <BusinessLogoDisplay
                    logoUrl={business.logo_url}
                    businessName={business.business_name}
                    size="md"
                  />
                  <div>
                    <CardTitle className="text-xl">{business.business_name}</CardTitle>
                    {business.contact_email && (
                      <CardDescription>{business.contact_email}</CardDescription>
                    )}
                  </div>
                </div>
                <div 
                  className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => e.stopPropagation()}>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setManagingImages(business)}
                    title="Manage Images"
                  >
                    <Images className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setEditingBusiness(business)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(business.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-2 gap-4">
                {business.website && (
                  <>
                    <dt className="text-sm font-medium text-muted-foreground">Website</dt>
                    <dd className="text-sm">
                      <a 
                        href={business.website} 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="text-primary hover:underline"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {business.website}
                      </a>
                    </dd>
                  </>
                )}
                {business.contact_phone && (
                  <>
                    <dt className="text-sm font-medium text-muted-foreground">Phone</dt>
                    <dd className="text-sm">{business.contact_phone}</dd>
                  </>
                )}
                {business.city && (
                  <>
                    <dt className="text-sm font-medium text-muted-foreground">Location</dt>
                    <dd className="text-sm">
                      {[business.city, business.postcode].filter(Boolean).join(', ')}
                    </dd>
                  </>
                )}
                {/* Primary Net zero category */}
                {business.primary_net_zero_category && (
                  <>
                    <dt className="text-sm font-medium text-muted-foreground">Primary Net zero category</dt>
                    <dd className="text-sm">{business.primary_net_zero_category}</dd>
                  </>
                )}
              </dl>
            </CardContent>
          </Card>
        ))}
      </div>

      <AlertDialog open={!!editingBusiness} onOpenChange={(open) => !open && setEditingBusiness(null)}>
        <AlertDialogContent className="max-w-4xl h-[85vh] p-6 gap-0 flex flex-col">
          <AlertDialogHeader className="flex-none">
            <AlertDialogTitle>Edit Business</AlertDialogTitle>
            <AlertDialogDescription>
              Make changes to your business information below.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex-1 overflow-y-auto -mx-6 px-6">
            {editingBusiness && (
              <BusinessForm
                initialData={editingBusiness}
                onSubmit={() => {
                  setEditingBusiness(null);
                  fetchBusinesses();
                }}
                onCancel={() => setEditingBusiness(null)}
                onUpgradeToSubscriptions={onUpgradeToSubscriptions}
              />
            )}
          </div>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={!!managingImages} onOpenChange={(open) => !open && setManagingImages(null)}>
        <AlertDialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <AlertDialogHeader>
            <AlertDialogTitle>Manage Business Images</AlertDialogTitle>
            <AlertDialogDescription>
              Upload and manage your business logo and product images.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {managingImages && (
            <BusinessImageManager
              business={managingImages}
              onImagesUpdated={() => {
                fetchBusinesses();
                // Update the managingImages state with fresh data
                const updatedBusiness = businesses.find(b => b.id === managingImages.id);
                if (updatedBusiness) {
                  setManagingImages(updatedBusiness);
                }
              }}
            />
          )}
          <AlertDialogFooter>
            <Button onClick={() => setManagingImages(null)}>
              Done
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default BusinessList;
