import { useState, useEffect, useCallback } from 'react';
import { SocialService } from '../services/socialService';
import { useAuth } from '@/contexts/AuthContext';
import type {
  UserConnection,
  ConnectionStats,
  UseConnectionsReturn
} from '../types/social.types';

export function useConnections(userId?: string): UseConnectionsReturn {
  const [connections, setConnections] = useState<UserConnection[]>([]);
  const [connectionStats, setConnectionStats] = useState<ConnectionStats>({
    follower_count: 0,
    following_count: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [followingMap, setFollowingMap] = useState<Map<string, boolean>>(new Map());
  const { user, loading: authLoading } = useAuth();

  const targetUserId = userId || user?.id;

  const loadConnectionStats = useCallback(async () => {
    if (!targetUserId) return;

    try {
      const stats = await SocialService.getConnectionStats(targetUserId);
      setConnectionStats(stats);
    } catch (err) {
      console.error('Error loading connection stats:', err);
    }
  }, [targetUserId]);

  const loadFollowing = useCallback(async () => {
    if (!targetUserId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await SocialService.getFollowing(targetUserId, { limit: 100 });
      setConnections(response.data);

      // Build following map for quick lookups
      const map = new Map<string, boolean>();
      response.data.forEach(connection => {
        map.set(connection.id, true);
      });
      setFollowingMap(map);
    } catch (err) {
      console.error('Error loading following:', err);
      setError(err instanceof Error ? err.message : 'Failed to load connections');
    } finally {
      setLoading(false);
    }
  }, [targetUserId]);

  const refresh = useCallback(async () => {
    await Promise.all([
      loadConnectionStats(),
      loadFollowing()
    ]);
  }, [loadConnectionStats, loadFollowing]);

  const isFollowing = useCallback((userId: string): boolean => {
    return followingMap.get(userId) || false;
  }, [followingMap]);

  const followUser = useCallback(async (userId: string): Promise<void> => {
    if (!user) {
      throw new Error('Must be authenticated to follow users');
    }

    try {
      await SocialService.followUser(userId);
      
      // Update local state optimistically
      setFollowingMap(prev => new Map(prev).set(userId, true));
      setConnectionStats(prev => ({
        ...prev,
        following_count: prev.following_count + 1
      }));

      // If we're viewing the current user's connections, add to the list
      if (targetUserId === user.id) {
        // We would need to fetch the user details to add to connections
        // For now, just refresh the data
        await refresh();
      }
    } catch (err) {
      console.error('Error following user:', err);
      throw err;
    }
  }, [user, targetUserId, refresh]);

  const unfollowUser = useCallback(async (userId: string): Promise<void> => {
    if (!user) {
      throw new Error('Must be authenticated to unfollow users');
    }

    try {
      await SocialService.unfollowUser(userId);
      
      // Update local state optimistically
      setFollowingMap(prev => {
        const newMap = new Map(prev);
        newMap.delete(userId);
        return newMap;
      });
      setConnectionStats(prev => ({
        ...prev,
        following_count: Math.max(0, prev.following_count - 1)
      }));

      // If we're viewing the current user's connections, remove from the list
      if (targetUserId === user.id) {
        setConnections(prev => prev.filter(connection => connection.id !== userId));
      }
    } catch (err) {
      console.error('Error unfollowing user:', err);
      throw err;
    }
  }, [user, targetUserId]);

  // Load data when component mounts or target user changes
  useEffect(() => {
    if (!authLoading && targetUserId) {
      refresh();
    }
  }, [authLoading, targetUserId, refresh]);

  return {
    connections,
    connectionStats,
    loading,
    error,
    isFollowing,
    followUser,
    unfollowUser,
    refresh
  };
}

// Hook specifically for checking if current user follows another user
export function useIsFollowing(userId: string) {
  const [isFollowing, setIsFollowing] = useState(false);
  const [loading, setLoading] = useState(true);
  const { user, loading: authLoading } = useAuth();

  useEffect(() => {
    const checkFollowStatus = async () => {
      if (!user || !userId || user.id === userId || authLoading) {
        setIsFollowing(false);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const following = await SocialService.isFollowing(userId);
        setIsFollowing(following);
      } catch (err) {
        console.error('Error checking follow status:', err);
        setIsFollowing(false);
      } finally {
        setLoading(false);
      }
    };

    checkFollowStatus();
  }, [user, userId, authLoading]);

  const toggleFollow = useCallback(async () => {
    if (!user || !userId || user.id === userId) return;

    try {
      if (isFollowing) {
        await SocialService.unfollowUser(userId);
        setIsFollowing(false);
      } else {
        await SocialService.followUser(userId);
        setIsFollowing(true);
      }
    } catch (err) {
      console.error('Error toggling follow status:', err);
      throw err;
    }
  }, [user, userId, isFollowing]);

  return {
    isFollowing,
    loading,
    toggleFollow
  };
}
