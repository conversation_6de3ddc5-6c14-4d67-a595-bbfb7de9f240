import { useState, useEffect, useCallback } from 'react';
import { SocialService } from '../services/socialService';
import { useAuth } from '@/contexts/AuthContext';
import type { PostReactions, ReactionType } from '../types/social.types';

export function usePostReactions(postId: string) {
  const [reactions, setReactions] = useState<PostReactions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, loading: authLoading } = useAuth();

  const loadReactions = useCallback(async () => {
    if (!postId) return;

    try {
      setLoading(true);
      setError(null);
      
      const postReactions = await SocialService.getPostReactions(postId);
      setReactions(postReactions);
    } catch (err) {
      console.error('Error loading post reactions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load reactions');
    } finally {
      setLoading(false);
    }
  }, [postId]);

  const toggleReaction = useCallback(async (reactionType: ReactionType): Promise<boolean> => {
    if (!user) {
      throw new Error('Must be authenticated to react to posts');
    }

    try {
      const isNowActive = await SocialService.toggleReaction(postId, reactionType);
      
      // Refresh reactions to get updated counts
      await loadReactions();
      
      return isNowActive;
    } catch (err) {
      console.error('Error toggling reaction:', err);
      throw err;
    }
  }, [user, postId, loadReactions]);

  const addReaction = useCallback(async (reactionType: ReactionType) => {
    if (!user) {
      throw new Error('Must be authenticated to react to posts');
    }

    try {
      await SocialService.addReaction(postId, reactionType);
      await loadReactions();
    } catch (err) {
      console.error('Error adding reaction:', err);
      throw err;
    }
  }, [user, postId, loadReactions]);

  const removeReaction = useCallback(async (reactionType: ReactionType) => {
    if (!user) {
      throw new Error('Must be authenticated to remove reactions');
    }

    try {
      await SocialService.removeReaction(postId, reactionType);
      await loadReactions();
    } catch (err) {
      console.error('Error removing reaction:', err);
      throw err;
    }
  }, [user, postId, loadReactions]);

  // Load reactions when component mounts or dependencies change
  useEffect(() => {
    if (!authLoading && postId) {
      loadReactions();
    }
  }, [authLoading, postId, loadReactions]);

  return {
    reactions,
    loading,
    error,
    toggleReaction,
    addReaction,
    removeReaction,
    refresh: loadReactions
  };
}

// Simplified hook for just checking if user has reacted
export function useUserReactions(postId: string) {
  const [userReactions, setUserReactions] = useState<ReactionType[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, loading: authLoading } = useAuth();

  const loadUserReactions = useCallback(async () => {
    if (!user || !postId) {
      setUserReactions([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const reactions = await SocialService.getUserReactions(postId);
      setUserReactions(reactions);
    } catch (err) {
      console.error('Error loading user reactions:', err);
      setUserReactions([]);
    } finally {
      setLoading(false);
    }
  }, [user, postId]);

  const hasReaction = useCallback((reactionType: ReactionType): boolean => {
    return userReactions.includes(reactionType);
  }, [userReactions]);

  const toggleReaction = useCallback(async (reactionType: ReactionType): Promise<boolean> => {
    if (!user) {
      throw new Error('Must be authenticated to react to posts');
    }

    try {
      const isNowActive = await SocialService.toggleReaction(postId, reactionType);
      
      // Update local state optimistically
      if (isNowActive) {
        setUserReactions(prev => [...prev.filter(r => r !== reactionType), reactionType]);
      } else {
        setUserReactions(prev => prev.filter(r => r !== reactionType));
      }
      
      return isNowActive;
    } catch (err) {
      console.error('Error toggling reaction:', err);
      // Refresh on error to get correct state
      await loadUserReactions();
      throw err;
    }
  }, [user, postId, loadUserReactions]);

  useEffect(() => {
    if (!authLoading) {
      loadUserReactions();
    }
  }, [authLoading, loadUserReactions]);

  return {
    userReactions,
    loading,
    hasReaction,
    toggleReaction,
    refresh: loadUserReactions
  };
}
