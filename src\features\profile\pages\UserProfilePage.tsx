import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

// Contexts and Hooks
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

// Integrations
import { supabase } from '@/integrations/supabase/client';

// UI Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Local Components
import AccountSettings from '../components/AccountSettings';
import Notifications from '../components/Notifications';
import ProfileForm from '../components/ProfileForm';
import Sidebar from '../components/Sidebar';
import FundingManagement from '../components/FundingManagement';
import { BusinessManagement } from '@/features/businessDirectory/components';
import { EventManagement } from '@/features/events/components';
import { FundingOpportunityService } from '@/features/fundingFinder/services/fundingOpportunityService';
import PlatformFeedback from '../components/PlatformFeedback';
import SubscriptionManagement from '../components/SubscriptionManagement';

// Notification system
import { useUnreadNotificationCount } from '@/hooks/useNotifications';

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  job_title: string | null;
  sustainability_professional: boolean;
  avatar_url: string | null;
  avatar_cloudflare_key: string | null;
  profile_visible: boolean | null;
  newsletter_subscribed: boolean | null;
  show_business_menu: boolean | null;
  bio: string | null;
  headline_bio: string | null;
  organisation_name: string | null;
  linkedin_url: string | null;
  twitter_url: string | null;
  instagram_url: string | null;
  tiktok_url: string | null;
  location_id: string | null;
  main_industry_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

const UserProfilePage = () => {
  console.log('🔄 UserProfilePage component rendering...');
  
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [canAddFunding, setCanAddFunding] = useState(false);
  const [canAddEvents, setCanAddEvents] = useState(false);

  const { user, signOut, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get unread notification count for sidebar
  const { unreadCount: unreadNotificationCount } = useUnreadNotificationCount();

  useEffect(() => {
    console.log('👤 UserProfilePage - Auth check:', { user: !!user, userId: user?.id, authLoading });
    
    // Don't do anything while auth is still loading
    if (authLoading) {
      console.log('⏳ Auth still loading, waiting...');
      return;
    }
    
    // Only redirect if auth is done loading AND there's no user
    if (!authLoading && !user) {
      console.log('❌ No user found after auth check, redirecting to auth');
      navigate('/auth');
      return;
    }

    // Only proceed if we have a user
    if (user) {
      console.log('✅ User authenticated, loading profile');
      fetchProfile();
      checkFundingPermissions();
      checkEventPermissions();
    }
  }, [user, navigate, authLoading]);

  // Handle URL query parameters (tab, success, canceled)
  useEffect(() => {
    const tab = searchParams.get('tab');
    const success = searchParams.get('success');
    const canceled = searchParams.get('canceled');

    console.log('🔍 ProfilePage URL params:', { tab, success, canceled, currentUrl: window.location.href });

    // Set the active tab if specified in URL
    if (tab) {
      console.log('📋 Setting active tab to:', tab);
      setActiveTab(tab);
    }

    // Show success message for successful payment
    if (success === 'true') {
      console.log('🎉 Showing payment success message');
      toast({
        title: "Payment Successful!",
        description: "Your subscription has been activated successfully.",
        variant: "default"
      });
      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('success');
      window.history.replaceState({}, '', newUrl);
    }

    // Show canceled message for canceled payment
    if (canceled === 'true') {
      toast({
        title: "Payment Canceled",
        description: "Your payment was canceled. No charges were made.",
        variant: "destructive"
      });
      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('canceled');
      window.history.replaceState({}, '', newUrl);
    }
  }, [searchParams, toast]);

  // Show loading while auth is being checked
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  const fetchProfile = async () => {
    if (!user) return;
    
    setLoading(true);
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      toast({
        title: "Error",
        description: "Failed to load profile",
        variant: "destructive"
      });
    } else {
      setProfile(data as Profile);
    }
    setLoading(false);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const checkFundingPermissions = async () => {
    try {
      const canAdd = await FundingOpportunityService.canUserAddFunding();
      setCanAddFunding(canAdd);
    } catch (error) {
      console.error('Error checking funding permissions:', error);
    }
  };

  const checkEventPermissions = async () => {
    if (!user) return;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('can_add_events')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      
      // @ts-ignore - Types will be updated after running supabase gen types
      setCanAddEvents(data?.can_add_events ?? true);
    } catch (error) {
      console.error('Error checking event permissions:', error);
      setCanAddEvents(true); // Default to true if error
    }
  };

  const refreshProfile = async () => {
    if (!user) return;
    await fetchProfile();
    await checkFundingPermissions();
    await checkEventPermissions();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  return (
    <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-screen bg-background">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">Your Profile</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div>
            <Sidebar
              firstName={profile?.first_name}
              lastName={profile?.last_name}
              email={user?.email}
              jobTitle={profile?.job_title}
              avatarUrl={profile?.avatar_url}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              showBusinessMenu={profile?.show_business_menu ?? true}
              canAddFunding={canAddFunding}
              showEventMenu={canAddEvents}
              unreadNotificationCount={unreadNotificationCount}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {activeTab === 'notifications' && (
              <Notifications />
            )}
            
            {activeTab === 'account' && user && (
              <AccountSettings
                userId={user.id}
                signOut={signOut}
                onSettingsUpdated={() => {
                  // Refresh profile to update sidebar visibility and funding permissions
                  refreshProfile();
                }}
              />
            )}

            {activeTab === 'funding' && canAddFunding && (
              <FundingManagement />
            )}

            {activeTab === 'profile' && (
              <Card>
                <CardHeader>
                  <CardTitle>Profile</CardTitle>
                  <CardDescription>
                    Manage your profile information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="mb-4">
                      <Label>Email</Label>
                      <Input
                        type="email"
                        value={user?.email || ''}
                        disabled
                      />
                    </div>
                    
                    {profile && (
                      <ProfileForm
                        id={user!.id}
                        initialFirstName={profile.first_name || ''}
                        initialLastName={profile.last_name || ''}
                        initialJobTitle={profile.job_title || ''}
                        initialSustainabilityProfessional={profile.sustainability_professional || false}
                        initialAvatarUrl={profile.avatar_url}
                        initialAvatarCloudflareKey={profile.avatar_cloudflare_key}
                        initialBio={profile.bio}
                        initialHeadlineBio={profile.headline_bio}
                        initialOrganisationName={profile.organisation_name}
                        initialLinkedinUrl={profile.linkedin_url}
                        initialTwitterUrl={profile.twitter_url}
                        initialInstagramUrl={profile.instagram_url}
                        initialTiktokUrl={profile.tiktok_url}
                        initialLocation={profile.location_id}
                        initialMainIndustryId={profile.main_industry_id}
                        onProfileUpdated={refreshProfile}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === 'business' && profile?.show_business_menu && (
              <BusinessManagement onUpgradeToSubscriptions={() => setActiveTab('subscriptions')} />
            )}

            {activeTab === 'events' && canAddEvents && (
              <EventManagement />
            )}

            {activeTab === 'subscriptions' && (
              <SubscriptionManagement />
            )}

            {activeTab === 'feedback' && (
              <PlatformFeedback />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;
