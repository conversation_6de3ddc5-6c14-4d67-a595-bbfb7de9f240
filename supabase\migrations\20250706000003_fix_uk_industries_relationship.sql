-- Fix UK Industries Self-Reference Relationship (Social Feed Only)
-- This migration creates a view for social feed queries without affecting existing components

-- Create a view that flattens the industry hierarchy for social feed queries
-- This avoids the self-referencing join issues in Supabase
CREATE OR REPLACE VIEW public.uk_industries_with_parent AS
SELECT
    i.id,
    i.name,
    i.description,
    i.sort_order,
    i.parent_id,
    p.name as parent_name,
    i.created_at,
    i.updated_at
FROM public.uk_industries i
LEFT JOIN public.uk_industries p ON i.parent_id = p.id;

-- Grant permissions on the view
GRANT SELECT ON public.uk_industries_with_parent TO authenticated;
GRANT SELECT ON public.uk_industries_with_parent TO anon;
GRANT SELECT ON public.uk_industries_with_parent TO service_role;

-- Add comment for documentation
COMMENT ON VIEW public.uk_industries_with_parent IS 'UK industries with parent industry name for social feed queries (avoids self-referencing join issues)';
