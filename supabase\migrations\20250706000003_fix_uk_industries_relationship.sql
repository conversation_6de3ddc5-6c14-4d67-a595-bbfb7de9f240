-- Fix UK Industries Self-Reference Relationship
-- This migration fixes the self-referencing relationship issue in uk_industries table

-- First, let's ensure the foreign key constraint is properly named and defined
ALTER TABLE public.uk_industries 
DROP CONSTRAINT IF EXISTS uk_industries_parent_id_fkey;

-- Re-add the foreign key constraint with a proper name
ALTER TABLE public.uk_industries 
ADD CONSTRAINT uk_industries_parent_id_fkey 
FOREIGN KEY (parent_id) REFERENCES public.uk_industries(id) ON DELETE CASCADE;

-- Create an index on parent_id for better performance
CREATE INDEX IF NOT EXISTS idx_uk_industries_parent_id ON public.uk_industries(parent_id);

-- Update the social service query to use a simpler approach for industries
-- We'll create a view that flattens the industry hierarchy for easier querying

CREATE OR REPLACE VIEW public.uk_industries_with_parent AS
SELECT 
    i.id,
    i.name,
    i.description,
    i.sort_order,
    i.parent_id,
    p.name as parent_name,
    i.created_at,
    i.updated_at
FROM public.uk_industries i
LEFT JOIN public.uk_industries p ON i.parent_id = p.id;

-- Grant permissions on the view
GRANT SELECT ON public.uk_industries_with_parent TO authenticated;
GRANT SELECT ON public.uk_industries_with_parent TO anon;
GRANT SELECT ON public.uk_industries_with_parent TO service_role;

-- Add comment for documentation
COMMENT ON VIEW public.uk_industries_with_parent IS 'UK industries with parent industry name for easier querying without complex joins';
