import { loadStripe, <PERSON>e } from '@stripe/stripe-js';
import { supabase } from '@/integrations/supabase/client';

// Types for our subscription system
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  plan_type: 'free' | 'business_premium' | 'advertising' | 'advertising_large' | 'advertising_medium' | 'advertising_small';
  stripe_price_id: string | null;
  price_amount: number;
  currency: string;
  billing_interval: string;
  features: string[];
  is_active: boolean;
  sort_order: number;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'incomplete' | 'incomplete_expired' | 'trialing';
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
  canceled_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface PaymentHistory {
  id: string;
  user_id: string;
  subscription_id: string | null;
  stripe_payment_intent_id: string | null;
  stripe_invoice_id: string | null;
  amount: number;
  currency: string;
  status: 'succeeded' | 'pending' | 'failed' | 'canceled' | 'refunded';
  description: string | null;
  payment_date: string | null;
  created_at: string;
}

class StripeService {
  private stripe: Stripe | null = null;
  private stripePromise: Promise<Stripe | null>;

  constructor() {
    const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
    if (!publishableKey) {
      console.warn('Stripe publishable key not found in environment variables');
      this.stripePromise = Promise.resolve(null);
    } else {
      this.stripePromise = loadStripe(publishableKey);
    }
  }

  async getStripe(): Promise<Stripe | null> {
    if (!this.stripe) {
      this.stripe = await this.stripePromise;
    }
    return this.stripe;
  }

  // Fetch all available subscription plans
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw new Error('Failed to fetch subscription plans');
    }
  }

  // Get user's current subscription
  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      console.log('🔍 Fetching subscription for user:', userId);

      // First, let's check what subscriptions exist for this user (any status)
      const { data: allSubs, error: allError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      console.log('All subscriptions for user:', allSubs);

      if (allError) {
        console.error('Error fetching all subscriptions:', allError);
      }

      // Get all active subscriptions and find the best one
      const { data: activeSubscriptions, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .in('status', ['active', 'incomplete'])
        .order('updated_at', { ascending: false });

      if (error) {
        console.warn('Supabase error in getUserSubscription:', error);
        if (error.code === 'PGRST116') {
          console.log('No active subscriptions found for user');
          return null;
        }
        console.error('Error querying user_subscriptions:', error);
        return null;
      }

      if (!activeSubscriptions || activeSubscriptions.length === 0) {
        console.log('No active subscriptions found');
        return null;
      }

      // If multiple subscriptions, prefer the most recently updated active one
      const data = activeSubscriptions.find(sub => sub.status === 'active') || activeSubscriptions[0];

      console.log('✅ Selected subscription:', {
        id: data.id,
        plan_id: data.plan_id,
        status: data.status,
        stripe_subscription_id: data.stripe_subscription_id
      });

      return data || null;
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      return null; // Return null instead of throwing to prevent UI crashes
    }
  }

  // Get all user subscriptions (for users with multiple subscriptions)
  async getAllUserSubscriptions(userId: string): Promise<UserSubscription[]> {
    try {
      console.log('🔍 Fetching all subscriptions for user:', userId);

      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching all user subscriptions:', error);
        return [];
      }

      console.log('✅ Found all subscriptions:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error fetching all user subscriptions:', error);
      return [];
    }
  }

  // Get user's payment history
  async getPaymentHistory(userId: string): Promise<PaymentHistory[]> {
    try {
      const { data, error } = await supabase
        .from('payment_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw new Error('Failed to fetch payment history');
    }
  }

  // Create a checkout session for subscription
  async createCheckoutSession(planId: string, userId: string): Promise<{ sessionId: string }> {
    try {
      // First, get the plan details
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .single();

      if (planError || !plan) {
        throw new Error('Subscription plan not found');
      }

      if (!plan.stripe_price_id) {
        throw new Error('Stripe price ID not configured for this plan');
      }

      // Call our working Supabase Edge Function to create the checkout session
      const requestBody = {
        priceId: plan.stripe_price_id,
        userId: userId,
        planId: planId,
        successUrl: `${window.location.origin}/profile?tab=subscriptions&success=true`,
        cancelUrl: `${window.location.origin}/profile?tab=subscriptions&canceled=true`
      };
      
      console.log('🔗 Creating checkout session with URLs:', requestBody);
      
      const { data, error } = await supabase.functions.invoke('create-checkout-session-final', {
        body: requestBody
      });

      if (error) throw error;
      return { sessionId: data.sessionId };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error('Failed to create checkout session');
    }
  }

  // Redirect to Stripe Checkout
  async redirectToCheckout(sessionId: string): Promise<void> {
    const stripe = await this.getStripe();
    if (!stripe) {
      throw new Error('Stripe not initialized');
    }

    const { error } = await stripe.redirectToCheckout({ sessionId });
    if (error) {
      console.error('Error redirecting to checkout:', error);
      throw new Error('Failed to redirect to checkout');
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string): Promise<void> {
    try {
      const { data, error } = await supabase.functions.invoke('cancel-subscription-final', {
        body: { subscriptionId }
      });

      if (error) throw error;
      if (!data.success) throw new Error(data.error || 'Failed to cancel subscription');
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  // Reactivate subscription (if canceled but not yet expired)
  async reactivateSubscription(subscriptionId: string): Promise<void> {
    try {
      const { data, error } = await supabase.functions.invoke('reactivate-subscription-final', {
        body: { subscriptionId }
      });

      if (error) throw error;
      if (!data.success) throw new Error(data.error || 'Failed to reactivate subscription');
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      throw new Error('Failed to reactivate subscription');
    }
  }

  // Update payment method
  async updatePaymentMethod(customerId: string): Promise<{ url: string }> {
    try {
      const { data, error } = await supabase.functions.invoke('create-portal-session-final', {
        body: {
          customerId,
          returnUrl: `${window.location.origin}/profile?tab=subscriptions`
        }
      });

      if (error) throw error;
      if (!data.success) throw new Error(data.error || 'Failed to create portal session');
      return { url: data.url };
    } catch (error) {
      console.error('Error creating portal session:', error);
      throw new Error('Failed to create portal session');
    }
  }

  // Check if user has active subscription for a specific plan type
  async hasActivePlan(userId: string, planType: 'business_premium' | 'advertising' | 'advertising_large' | 'advertising_medium' | 'advertising_small'): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans!inner(plan_type)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .eq('subscription_plans.plan_type', planType)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking active plan:', error);
      return false;
    }
  }

  // Check if user has any active advertising subscription
  async hasActiveAdvertisingPlan(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans!inner(plan_type)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .in('subscription_plans.plan_type', ['advertising_large', 'advertising_medium', 'advertising_small'])
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking active advertising plan:', error);
      return false;
    }
  }

  // Get subscription usage for a specific feature
  async getUsageCount(userId: string, featureType: string): Promise<number> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const { data, error } = await supabase
        .from('subscription_usage')
        .select('usage_count')
        .eq('user_id', userId)
        .eq('feature_type', featureType)
        .gte('usage_period_start', startOfMonth.toISOString())
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No data found - return 0
          return 0;
        }
        console.warn('Subscription usage query error:', error);
        return 0;
      }
      return data?.usage_count || 0;
    } catch (error) {
      console.warn('Error fetching usage count (returning 0):', error);
      return 0;
    }
  }

  // Increment usage count for a feature
  async incrementUsage(userId: string, featureType: string): Promise<void> {
    try {
      const { error } = await supabase.functions.invoke('increment-usage', {
        body: { userId, featureType }
      });

      if (error) throw error;
    } catch (error) {
      console.error('Error incrementing usage:', error);
      throw new Error('Failed to update usage count');
    }
  }
}

export const stripeService = new StripeService();
export default stripeService;
