import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { UserPlus, UserMinus, Loader2 } from 'lucide-react';
import { useIsFollowing } from '../hooks/useConnections';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface FollowButtonProps {
  userId: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  showIcon?: boolean;
  className?: string;
  onFollowChange?: (isFollowing: boolean) => void;
}

const FollowButton: React.FC<FollowButtonProps> = ({
  userId,
  variant = 'outline',
  size = 'sm',
  showIcon = true,
  className,
  onFollowChange
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { isFollowing, loading, toggleFollow } = useIsFollowing(userId);

  // Don't render if user is not authenticated or trying to follow themselves
  if (!user || user.id === userId) {
    return null;
  }

  const handleClick = async () => {
    try {
      await toggleFollow();
      
      const newFollowState = !isFollowing;
      onFollowChange?.(newFollowState);
      
      toast({
        title: newFollowState ? "Following" : "Unfollowed",
        description: newFollowState 
          ? "You are now following this user" 
          : "You have unfollowed this user"
      });
    } catch (error) {
      console.error('Error toggling follow:', error);
      toast({
        title: "Error",
        description: "Failed to update follow status. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        disabled
      >
        <Loader2 size={16} className={showIcon ? "mr-2 animate-spin" : "animate-spin"} />
        {showIcon && "Loading..."}
      </Button>
    );
  }

  return (
    <Button
      variant={isFollowing ? 'default' : variant}
      size={size}
      className={className}
      onClick={handleClick}
    >
      {showIcon && (
        isFollowing ? (
          <UserMinus size={16} className="mr-2" />
        ) : (
          <UserPlus size={16} className="mr-2" />
        )
      )}
      {isFollowing ? 'Following' : 'Follow'}
    </Button>
  );
};

export default FollowButton;
