import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface UserProfileLinkProps {
  userId: string;
  firstName?: string | null;
  lastName?: string | null;
  jobTitle?: string | null;
  organisationName?: string | null;
  className?: string;
  showJobTitle?: boolean;
  showOrganisation?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const UserProfileLink: React.FC<UserProfileLinkProps> = ({
  userId,
  firstName,
  lastName,
  jobTitle,
  organisationName,
  className = '',
  showJobTitle = true,
  showOrganisation = true,
  size = 'md'
}) => {
  const { user } = useAuth();
  
  // Check if this is the current user
  const isCurrentUser = user?.id === userId;
  
  // Determine if profile is visible (for now, assume all profiles are visible)
  // TODO: Add profile visibility check when that feature is implemented
  const isProfileVisible = true;
  
  const displayName = firstName && lastName 
    ? `${firstName} ${lastName}` 
    : firstName || lastName || 'Anonymous User';

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm', 
    lg: 'text-base'
  };

  const linkClasses = `${sizeClasses[size]} ${className}`;

  const UserInfo = () => (
    <div className="flex flex-col">
      <span className={`font-semibold ${isProfileVisible ? 'hover:underline' : ''}`}>
        {displayName}
      </span>
      {showJobTitle && jobTitle && (
        <span className={`${sizeClasses[size]} text-muted-foreground`}>
          {jobTitle}
        </span>
      )}
      {showOrganisation && organisationName && (
        <span className={`${sizeClasses[size]} text-muted-foreground`}>
          {organisationName}
        </span>
      )}
    </div>
  );

  // If profile is not visible, don't make it a link
  if (!isProfileVisible) {
    return <div className={linkClasses}><UserInfo /></div>;
  }

  return (
    <Link
      to={`/members/${userId}`}
      className={`${linkClasses} transition-colors hover:text-primary`}
    >
      <UserInfo />
    </Link>
  );
};

export default UserProfileLink;
