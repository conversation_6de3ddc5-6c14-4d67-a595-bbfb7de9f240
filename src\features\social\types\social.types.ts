// Social Feed & Connections types for the Net Zero Nexus Hub
import { Database } from '@/integrations/supabase/types';

// Database table types
export type SocialPost = Database['public']['Tables']['social_posts']['Row'];
export type SocialComment = Database['public']['Tables']['social_comments']['Row'];
export type SocialFollow = Database['public']['Tables']['social_follows']['Row'];
export type SocialPostNetZeroCategory = Database['public']['Tables']['social_post_netzero_categories']['Row'];
export type SocialPostIndustry = Database['public']['Tables']['social_post_industries']['Row'];
export type SocialPostReaction = Database['public']['Tables']['social_post_reactions']['Row'];

// Reaction types
export type ReactionType = 'like' | 'support' | 'love' | 'insightful';

export interface ReactionCounts {
  like_count: number;
  support_count: number;
  love_count: number;
  insightful_count: number;
  total_reaction_count: number;
}

// Insert types for creating new records
export type NewSocialPost = Database['public']['Tables']['social_posts']['Insert'];
export type NewSocialComment = Database['public']['Tables']['social_comments']['Insert'];
export type NewSocialFollow = Database['public']['Tables']['social_follows']['Insert'];
export type NewSocialPostNetZeroCategory = Database['public']['Tables']['social_post_netzero_categories']['Insert'];
export type NewSocialPostIndustry = Database['public']['Tables']['social_post_industries']['Insert'];
export type NewSocialPostReaction = Database['public']['Tables']['social_post_reactions']['Insert'];

// Update types for modifying existing records
export type UpdateSocialPost = Database['public']['Tables']['social_posts']['Update'];
export type UpdateSocialComment = Database['public']['Tables']['social_comments']['Update'];

// Extended types with joined data
export interface SocialPostWithAuthor extends SocialPost {
  author: {
    id: string;
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
    job_title: string | null;
    organisation_name: string | null;
  };
  categories?: Array<{
    id: string;
    name: string;
    category: {
      name: string;
    };
  }>;
  industries?: Array<{
    id: string;
    name: string;
    parent?: {
      name: string;
    };
  }>;
  user_reactions?: ReactionType[]; // Current user's reactions to this post
}

export interface SocialCommentWithAuthor extends SocialComment {
  author: {
    id: string;
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
    job_title: string | null;
    organisation_name: string | null;
  };
  replies?: SocialCommentWithAuthor[];
}

// Form data types
export interface PostFormData {
  content: string;
  netZeroCategoryIds: string[];
  industryIds: string[];
}

export interface CommentFormData {
  content: string;
}

// Reaction data types
export interface ReactionData {
  type: ReactionType;
  count: number;
  userHasReacted: boolean;
}

export interface PostReactions {
  like: ReactionData;
  support: ReactionData;
  love: ReactionData;
  insightful: ReactionData;
  total: number;
}

// Feed filtering and sorting options
export type FeedFilter = 'all' | 'following';
export type FeedSort = 'newest' | 'oldest' | 'most_commented';

export interface FeedFilters {
  filter: FeedFilter;
  sort: FeedSort;
  categoryIds?: string[];
  industryIds?: string[];
}

// Connection types
export interface UserConnection {
  id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  job_title: string | null;
  organisation_name: string | null;
  followed_at?: string;
  is_following?: boolean;
  follower_count?: number;
  following_count?: number;
}

export interface ConnectionStats {
  follower_count: number;
  following_count: number;
}

// API response types
export interface SocialFeedResponse {
  posts: SocialPostWithAuthor[];
  hasMore: boolean;
  nextCursor?: string;
}

export interface CommentThreadResponse {
  comments: SocialCommentWithAuthor[];
  hasMore: boolean;
  nextCursor?: string;
}

// Hook return types
export interface UseSocialFeedReturn {
  posts: SocialPostWithAuthor[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  createPost: (data: PostFormData) => Promise<SocialPost>;
  updatePost: (postId: string, data: Partial<PostFormData>) => Promise<void>;
  deletePost: (postId: string) => Promise<void>;
}

export interface UseCommentsReturn {
  comments: SocialCommentWithAuthor[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  createComment: (data: CommentFormData & { postId: string; parentCommentId?: string }) => Promise<SocialComment>;
  updateComment: (commentId: string, content: string) => Promise<void>;
  deleteComment: (commentId: string) => Promise<void>;
}

export interface UseConnectionsReturn {
  connections: UserConnection[];
  connectionStats: ConnectionStats;
  loading: boolean;
  error: string | null;
  isFollowing: (userId: string) => boolean;
  followUser: (userId: string) => Promise<void>;
  unfollowUser: (userId: string) => Promise<void>;
  refresh: () => Promise<void>;
}

// Notification types for social interactions
export interface SocialNotificationData {
  post_id?: string;
  comment_id?: string;
  user_id: string;
  user_name: string;
  post_content?: string;
}

// Error types
export interface SocialError {
  code: string;
  message: string;
  details?: any;
}

// Pagination types
export interface PaginationParams {
  limit?: number;
  cursor?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  hasMore: boolean;
  nextCursor?: string;
}

// Search and discovery types
export interface SocialSearchFilters {
  query?: string;
  categories?: string[];
  industries?: string[];
  users?: string[];
  dateFrom?: string;
  dateTo?: string;
}

export interface SocialSearchResult {
  posts: SocialPostWithAuthor[];
  users: UserConnection[];
  totalResults: number;
}

// Real-time subscription types
export interface SocialSubscriptionEvent {
  type: 'post_created' | 'post_updated' | 'post_deleted' | 'comment_created' | 'comment_updated' | 'comment_deleted' | 'follow_created' | 'follow_deleted';
  data: SocialPost | SocialComment | SocialFollow;
  userId?: string;
}
