// Social Feature Exports
export { default as SocialFeedPage } from './pages/SocialFeedPage';

// Components
export { default as FollowButton } from './components/FollowButton';
export { default as PostReactions } from './components/PostReactions';

// Hooks
export { useSocialFeed } from './hooks/useSocialFeed';
export { useConnections, useIsFollowing } from './hooks/useConnections';
export { useComments, useCommentForm } from './hooks/useComments';
export { usePostReactions, useUserReactions } from './hooks/usePostReactions';

// Types
export type {
  SocialPost,
  SocialComment,
  SocialFollow,
  SocialPostReaction,
  SocialPostWithAuthor,
  SocialCommentWithAuthor,
  PostFormData,
  CommentFormData,
  FeedFilters,
  UserConnection,
  ConnectionStats,
  ReactionType,
  ReactionCounts,
  PostReactions,
  UseSocialFeedReturn,
  UseCommentsReturn,
  UseConnectionsReturn
} from './types/social.types';

// Services
export { SocialService } from './services/socialService';
