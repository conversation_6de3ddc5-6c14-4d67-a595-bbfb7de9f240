// Social Feature Exports
export { default as SocialFeedPage } from './pages/SocialFeedPage';

// Components
export { default as FollowButton } from './components/FollowButton';

// Hooks
export { useSocialFeed } from './hooks/useSocialFeed';
export { useConnections, useIsFollowing } from './hooks/useConnections';
export { useComments, useCommentForm } from './hooks/useComments';

// Types
export type {
  SocialPost,
  SocialComment,
  SocialFollow,
  SocialPostWithAuthor,
  SocialCommentWithAuthor,
  PostFormData,
  CommentFormData,
  FeedFilters,
  UserConnection,
  ConnectionStats,
  UseSocialFeedReturn,
  UseCommentsReturn,
  UseConnectionsReturn
} from './types/social.types';

// Services
export { SocialService } from './services/socialService';
