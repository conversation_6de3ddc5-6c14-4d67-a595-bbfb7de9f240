// Social Feature Exports
export { default as SocialFeedPage } from './pages/SocialFeedPage';

// Components
export { default as FollowButton } from './components/FollowButton';
export { default as PostReactions } from './components/PostReactions';
export { default as PostCreator } from './components/PostCreator';
export { default as SocialPost } from './components/SocialPost';
export { default as CommentSection } from './components/CommentSection';
export { default as UserProfileLink } from './components/UserProfileLink';

// Hooks
export { useSocialFeed } from './hooks/useSocialFeed';
export { useConnections, useIsFollowing } from './hooks/useConnections';
export { useComments, useCommentForm } from './hooks/useComments';
export { usePostReactions, useUserReactions } from './hooks/usePostReactions';

// Types
export type {
  SocialPost,
  SocialComment,
  SocialFollow,
  SocialPostReaction,
  SocialPostWithAuthor,
  SocialCommentWithAuthor,
  PostFormData,
  CommentFormData,
  FeedFilters,
  UserConnection,
  ConnectionStats,
  ReactionType,
  ReactionCounts,
  PostReactions,
  UseSocialFeedReturn,
  UseCommentsReturn,
  UseConnectionsReturn
} from './types/social.types';

// Services
export { SocialService } from './services/socialService';
