import { useState, useEffect, useCallback, useRef } from 'react';
import { SocialService } from '../services/socialService';
import { useAuth } from '@/contexts/AuthContext';
import type {
  SocialComment,
  SocialCommentWithAuthor,
  CommentFormData,
  UseCommentsReturn
} from '../types/social.types';

export function useComments(postId: string): UseCommentsReturn {
  const [comments, setComments] = useState<SocialCommentWithAuthor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursor, setNextCursor] = useState<string | undefined>();
  const { user, loading: authLoading } = useAuth();
  const postIdRef = useRef(postId);

  // Update postId ref when it changes
  postIdRef.current = postId;

  const loadComments = useCallback(async (reset = false) => {
    if (!postIdRef.current) return;

    try {
      setLoading(true);
      setError(null);

      const cursor = reset ? undefined : nextCursor;
      const response = await SocialService.getComments(postIdRef.current, {
        limit: 20,
        cursor
      });

      if (reset) {
        setComments(response.data);
      } else {
        setComments(prev => [...prev, ...response.data]);
      }

      setHasMore(response.hasMore);
      setNextCursor(response.nextCursor);
    } catch (err) {
      console.error('Error loading comments:', err);
      setError(err instanceof Error ? err.message : 'Failed to load comments');
    } finally {
      setLoading(false);
    }
  }, [nextCursor]);

  const refresh = useCallback(async () => {
    setNextCursor(undefined);
    await loadComments(true);
  }, [loadComments]);

  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    await loadComments(false);
  }, [hasMore, loading, loadComments]);

  const createComment = useCallback(async (
    data: CommentFormData & { postId: string; parentCommentId?: string }
  ): Promise<SocialComment> => {
    try {
      const newComment = await SocialService.createComment(
        data.postId,
        { content: data.content },
        data.parentCommentId
      );
      
      // Refresh comments to show the new comment in the correct position
      await refresh();
      
      return newComment;
    } catch (err) {
      console.error('Error creating comment:', err);
      throw err;
    }
  }, [refresh]);

  const updateComment = useCallback(async (commentId: string, content: string): Promise<void> => {
    try {
      await SocialService.updateComment(commentId, content);
      
      // Update the comment in the local state
      const updateCommentInTree = (comments: SocialCommentWithAuthor[]): SocialCommentWithAuthor[] => {
        return comments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              content,
              edited_at: new Date().toISOString()
            };
          }
          
          if (comment.replies && comment.replies.length > 0) {
            return {
              ...comment,
              replies: updateCommentInTree(comment.replies)
            };
          }
          
          return comment;
        });
      };

      setComments(prev => updateCommentInTree(prev));
    } catch (err) {
      console.error('Error updating comment:', err);
      throw err;
    }
  }, []);

  const deleteComment = useCallback(async (commentId: string): Promise<void> => {
    try {
      await SocialService.deleteComment(commentId);
      
      // Remove the comment from the local state
      const removeCommentFromTree = (comments: SocialCommentWithAuthor[]): SocialCommentWithAuthor[] => {
        return comments.filter(comment => {
          if (comment.id === commentId) {
            return false;
          }
          
          if (comment.replies && comment.replies.length > 0) {
            comment.replies = removeCommentFromTree(comment.replies);
          }
          
          return true;
        });
      };

      setComments(prev => removeCommentFromTree(prev));
    } catch (err) {
      console.error('Error deleting comment:', err);
      throw err;
    }
  }, []);

  // Load initial comments when component mounts or postId changes
  useEffect(() => {
    if (!authLoading && postId) {
      setComments([]);
      setNextCursor(undefined);
      setHasMore(true);
      refresh();
    }
  }, [authLoading, postId, refresh]);

  return {
    comments,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    createComment,
    updateComment,
    deleteComment
  };
}

// Helper hook for managing comment form state
export function useCommentForm(
  onSubmit: (data: CommentFormData) => Promise<void>,
  initialContent = ''
) {
  const [content, setContent] = useState(initialContent);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      setError('Comment cannot be empty');
      return;
    }

    if (content.length > 1000) {
      setError('Comment is too long (max 1000 characters)');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      
      await onSubmit({ content: content.trim() });
      setContent('');
    } catch (err) {
      console.error('Error submitting comment:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit comment');
    } finally {
      setIsSubmitting(false);
    }
  }, [content, onSubmit]);

  const reset = useCallback(() => {
    setContent(initialContent);
    setError(null);
    setIsSubmitting(false);
  }, [initialContent]);

  return {
    content,
    setContent,
    isSubmitting,
    error,
    handleSubmit,
    reset,
    canSubmit: content.trim().length > 0 && content.length <= 1000 && !isSubmitting
  };
}
