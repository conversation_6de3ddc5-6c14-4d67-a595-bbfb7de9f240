import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, Lock, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface SubscriptionUpgradePromptProps {
  feature: 'events' | 'images' | 'products' | 'advertising';
  message: string;
  currentUsage?: number;
  limit?: number;
  className?: string;
  onUpgradeToSubscriptions?: () => void;
}

const SubscriptionUpgradePrompt: React.FC<SubscriptionUpgradePromptProps> = ({
  feature,
  message,
  currentUsage,
  limit,
  className = '',
  onUpgradeToSubscriptions,
}) => {
  const navigate = useNavigate();

  const handleUpgrade = () => {
    if (onUpgradeToSubscriptions) {
      onUpgradeToSubscriptions();
    } else {
      navigate('/profile?tab=subscriptions');
    }
  };

  const getFeatureIcon = () => {
    switch (feature) {
      case 'events':
        return '📅';
      case 'images':
        return '🖼️';
      case 'products':
        return '📦';
      case 'advertising':
        return '📢';
      default:
        return '⭐';
    }
  };

  const getFeatureName = () => {
    switch (feature) {
      case 'events':
        return 'Events';
      case 'images':
        return 'Business Images';
      case 'products':
        return 'Products';
      case 'advertising':
        return 'Advertising';
      default:
        return 'Feature';
    }
  };

  return (
    <Card className={`border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50 ${className}`}>
      <CardHeader className="text-center">
        <div className="flex justify-center items-center gap-2 mb-2">
          <Lock className="h-5 w-5 text-yellow-600" />
          <Crown className="h-6 w-6 text-yellow-500" />
        </div>
        <CardTitle className="text-lg text-yellow-800">
          {getFeatureIcon()} {getFeatureName()} Limit Reached
        </CardTitle>
        <CardDescription className="text-yellow-700">
          {message}
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        {currentUsage !== undefined && limit !== undefined && limit > 0 && (
          <div className="bg-white/50 rounded-lg p-3">
            <div className="text-sm text-yellow-700 mb-1">Current Usage</div>
            <div className="flex items-center justify-center gap-2">
              <Badge variant="outline" className="border-yellow-300 text-yellow-800">
                {currentUsage} / {limit}
              </Badge>
              <div className="text-xs text-yellow-600">this month</div>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <div className="text-sm font-medium text-yellow-800">
            Upgrade to unlock:
          </div>
          <ul className="text-sm text-yellow-700 space-y-1">
            {feature === 'events' && (
              <>
                <li>✨ Unlimited events per month</li>
                <li>📈 Enhanced event analytics</li>
                <li>🎯 Priority event placement</li>
              </>
            )}
            {feature === 'images' && (
              <>
                <li>✨ Unlimited business images</li>
                <li>🖼️ High-resolution image uploads</li>
                <li>📱 Mobile-optimized galleries</li>
              </>
            )}
            {feature === 'products' && (
              <>
                <li>✨ Unlimited product showcase</li>
                <li>📊 Product performance analytics</li>
                <li>🔗 Enhanced product linking</li>
              </>
            )}
            {feature === 'advertising' && (
              <>
                <li>📢 Weekly featured placement</li>
                <li>🏠 Homepage banner space</li>
                <li>📧 Newsletter inclusion</li>
                <li>📊 Analytics dashboard</li>
              </>
            )}
          </ul>
        </div>

        <Button 
          onClick={handleUpgrade}
          className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white"
        >
          <Crown className="h-4 w-4 mr-2" />
          Upgrade Now
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>

        <div className="text-xs text-yellow-600">
          Starting from £5.99/month • Cancel anytime
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscriptionUpgradePrompt;
