import { useState, useEffect, useCallback, useRef } from 'react';
import { SocialService } from '../services/socialService';
import { useAuth } from '@/contexts/AuthContext';
import type {
  SocialPost,
  SocialPostWithAuthor,
  PostFormData,
  FeedFilters,
  UseSocialFeedReturn
} from '../types/social.types';

export function useSocialFeed(filters: FeedFilters = { filter: 'all', sort: 'newest' }): UseSocialFeedReturn {
  const [posts, setPosts] = useState<SocialPostWithAuthor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursor, setNextCursor] = useState<string | undefined>();
  const { user, loading: authLoading } = useAuth();
  const filtersRef = useRef(filters);

  // Update filters ref when filters change
  filtersRef.current = filters;

  const loadPosts = useCallback(async (reset = false) => {
    if (!user && filters.filter === 'following') {
      setPosts([]);
      setLoading(false);
      setHasMore(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const cursor = reset ? undefined : nextCursor;
      const response = await SocialService.getFeed(filtersRef.current, {
        limit: 20,
        cursor
      });

      if (reset) {
        setPosts(response.data);
      } else {
        setPosts(prev => [...prev, ...response.data]);
      }

      setHasMore(response.hasMore);
      setNextCursor(response.nextCursor);
    } catch (err) {
      console.error('Error loading social feed:', err);
      setError(err instanceof Error ? err.message : 'Failed to load posts');
    } finally {
      setLoading(false);
    }
  }, [user, nextCursor, filters.filter]);

  const refresh = useCallback(async () => {
    setNextCursor(undefined);
    await loadPosts(true);
  }, [loadPosts]);

  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    await loadPosts(false);
  }, [hasMore, loading, loadPosts]);

  const createPost = useCallback(async (data: PostFormData): Promise<SocialPost> => {
    try {
      const newPost = await SocialService.createPost(data);
      
      // Refresh the feed to show the new post
      await refresh();
      
      return newPost;
    } catch (err) {
      console.error('Error creating post:', err);
      throw err;
    }
  }, [refresh]);

  const updatePost = useCallback(async (postId: string, data: Partial<PostFormData>): Promise<void> => {
    try {
      await SocialService.updatePost(postId, data);
      
      // Update the post in the local state
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, content: data.content || post.content, edited_at: new Date().toISOString() }
          : post
      ));
    } catch (err) {
      console.error('Error updating post:', err);
      throw err;
    }
  }, []);

  const deletePost = useCallback(async (postId: string): Promise<void> => {
    try {
      await SocialService.deletePost(postId);
      
      // Remove the post from the local state
      setPosts(prev => prev.filter(post => post.id !== postId));
    } catch (err) {
      console.error('Error deleting post:', err);
      throw err;
    }
  }, []);

  // Load initial posts when component mounts or filters change
  useEffect(() => {
    if (!authLoading) {
      refresh();
    }
  }, [authLoading, filters.filter, filters.sort, refresh]);

  return {
    posts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    createPost,
    updatePost,
    deletePost
  };
}
