import React, { useState } from 'react';
import { MessageCircle, Users, Filter, Plus } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSocialFeed } from '../hooks/useSocialFeed';
import FollowButton from '../components/FollowButton';
import type { FeedFilters, SocialPostWithAuthor } from '../types/social.types';

// Placeholder components - will be implemented in next tasks
const PostCreator = ({ onPostCreated }: { onPostCreated: () => void }) => (
  <Card className="mb-6">
    <CardContent className="pt-6">
      <div className="flex items-center space-x-4">
        <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
          <Plus size={20} />
        </div>
        <div className="flex-1">
          <Button variant="outline" className="w-full justify-start text-muted-foreground">
            What's on your mind about sustainability?
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
);

const SocialPost = ({ post }: { post: SocialPostWithAuthor }) => {
  const { user } = useAuth();

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
            {post.author.first_name?.[0] || 'U'}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2">
                  <h4 className="font-semibold text-sm">
                    {post.author.first_name} {post.author.last_name}
                  </h4>
                  <span className="text-xs text-muted-foreground">
                    {new Date(post.created_at).toLocaleDateString()}
                    {post.edited_at && ' (edited)'}
                  </span>
                </div>
                {post.author.job_title && (
                  <p className="text-xs text-muted-foreground">{post.author.job_title}</p>
                )}
                {post.author.organisation_name && (
                  <p className="text-xs text-muted-foreground">{post.author.organisation_name}</p>
                )}
              </div>
              {user && user.id !== post.user_id && (
                <FollowButton userId={post.user_id} />
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm mb-3 whitespace-pre-wrap">{post.content}</p>

        {/* Categories and Industries */}
        {(post.categories?.length > 0 || post.industries?.length > 0) && (
          <div className="flex flex-wrap gap-1 mb-3">
            {post.categories?.map(category => (
              <Badge key={category.id} variant="secondary" className="text-xs">
                {category.category.name}: {category.name}
              </Badge>
            ))}
            {post.industries?.map(industry => (
              <Badge key={industry.id} variant="outline" className="text-xs">
                {industry.parent?.name ? `${industry.parent.name}: ` : ''}{industry.name}
              </Badge>
            ))}
          </div>
        )}

        <Separator className="my-3" />

        {/* Engagement Actions */}
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <Button variant="ghost" size="sm" className="h-8 px-2">
            <MessageCircle size={16} className="mr-1" />
            {post.comment_count || 0}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const SocialFilters = ({ 
  filters, 
  onFiltersChange 
}: { 
  filters: FeedFilters;
  onFiltersChange: (filters: FeedFilters) => void;
}) => (
  <Card className="mb-6">
    <CardHeader className="pb-3">
      <CardTitle className="text-sm flex items-center">
        <Filter size={16} className="mr-2" />
        Feed Filters
      </CardTitle>
    </CardHeader>
    <CardContent className="pt-0">
      <Tabs 
        value={filters.filter} 
        onValueChange={(value) => onFiltersChange({ ...filters, filter: value as 'all' | 'following' })}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="all">All Posts</TabsTrigger>
          <TabsTrigger value="following">Following</TabsTrigger>
        </TabsList>
      </Tabs>
    </CardContent>
  </Card>
);

const SocialFeedPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const [filters, setFilters] = useState<FeedFilters>({
    filter: 'all',
    sort: 'newest'
  });

  const {
    posts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    createPost
  } = useSocialFeed(filters);

  const handlePostCreated = () => {
    toast({
      title: "Post created",
      description: "Your post has been shared with the community."
    });
  };

  const handleFiltersChange = (newFilters: FeedFilters) => {
    setFilters(newFilters);
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadMore();
    }
  };

  if (authLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-32 bg-muted rounded"></div>
            <div className="h-48 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <MessageCircle size={64} className="mx-auto mb-4 text-muted-foreground" />
          <h1 className="text-2xl font-bold mb-2">Join the Conversation</h1>
          <p className="text-muted-foreground mb-6">
            Sign in to share your sustainability insights and connect with the community.
          </p>
          <Button onClick={() => window.location.href = '/auth'}>
            Sign In
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Social Feed</h1>
          <p className="text-muted-foreground">
            Connect with the sustainability community and share your insights.
          </p>
        </div>

        {/* Post Creator */}
        <PostCreator onPostCreated={handlePostCreated} />

        {/* Filters */}
        <SocialFilters filters={filters} onFiltersChange={handleFiltersChange} />

        {/* Feed */}
        {error ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle size={48} className="mx-auto mb-4 text-destructive" />
              <h3 className="text-lg font-semibold mb-2">Error loading posts</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={refresh}>Try Again</Button>
            </CardContent>
          </Card>
        ) : loading && posts.length === 0 ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex space-x-3 mb-4">
                    <div className="w-10 h-10 bg-muted rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-1/3"></div>
                      <div className="h-3 bg-muted rounded w-1/4"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : posts.length > 0 ? (
          <div>
            {posts.map(post => (
              <SocialPost key={post.id} post={post} />
            ))}

            {/* Load More */}
            {hasMore && (
              <div className="text-center mt-6">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  {loading ? 'Loading...' : 'Load More Posts'}
                </Button>
              </div>
            )}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle size={48} className="mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No posts yet</h3>
              <p className="text-muted-foreground mb-4">
                {filters.filter === 'following'
                  ? "Follow some users to see their posts here."
                  : "Be the first to share something with the community!"
                }
              </p>
              {filters.filter === 'following' && (
                <Button
                  variant="outline"
                  onClick={() => setFilters({ ...filters, filter: 'all' })}
                >
                  View All Posts
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SocialFeedPage;
