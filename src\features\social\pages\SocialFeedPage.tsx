import React, { useState } from 'react';
import { MessageCircle, Users, Filter, Plus } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSocialFeed } from '../hooks/useSocialFeed';
import PostCreator from '../components/PostCreator';
import SocialPost from '../components/SocialPost';
import type { FeedFilters, SocialPostWithAuthor, PostFormData } from '../types/social.types';

// SocialPost component is now imported from ../components/SocialPost

const SocialFilters = ({ 
  filters, 
  onFiltersChange 
}: { 
  filters: FeedFilters;
  onFiltersChange: (filters: FeedFilters) => void;
}) => (
  <Card className="mb-6">
    <CardHeader className="pb-3">
      <CardTitle className="text-sm flex items-center">
        <Filter size={16} className="mr-2" />
        Feed Filters
      </CardTitle>
    </CardHeader>
    <CardContent className="pt-0">
      <Tabs 
        value={filters.filter} 
        onValueChange={(value) => onFiltersChange({ ...filters, filter: value as 'all' | 'following' })}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="all">All Posts</TabsTrigger>
          <TabsTrigger value="following">Following</TabsTrigger>
        </TabsList>
      </Tabs>
    </CardContent>
  </Card>
);

const SocialFeedPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const [filters, setFilters] = useState<FeedFilters>({
    filter: 'all',
    sort: 'newest'
  });

  const {
    posts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    createPost
  } = useSocialFeed(filters);

  const handlePostCreated = async (postData: PostFormData) => {
    try {
      await createPost(postData);
      // The PostCreator component will show its own success toast
    } catch (error) {
      console.error('Error creating post:', error);
      // Error handling is done in the PostCreator component
      throw error; // Re-throw so PostCreator can handle it
    }
  };

  const handleFiltersChange = (newFilters: FeedFilters) => {
    setFilters(newFilters);
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadMore();
    }
  };

  const handleReactionChange = (postId: string) => {
    // Refresh the feed to get updated reaction counts
    refresh();
  };

  const handlePostUpdate = (postId: string) => {
    // Refresh the feed to get updated post content
    refresh();
  };

  const handlePostDelete = (postId: string) => {
    // Remove the post from local state immediately
    // The refresh will sync with server state
    refresh();
  };

  if (authLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-32 bg-muted rounded"></div>
            <div className="h-48 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <MessageCircle size={64} className="mx-auto mb-4 text-muted-foreground" />
          <h1 className="text-2xl font-bold mb-2">Join the Conversation</h1>
          <p className="text-muted-foreground mb-6">
            Sign in to share your sustainability insights and connect with the community.
          </p>
          <Button onClick={() => window.location.href = '/auth'}>
            Sign In
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Social Feed</h1>
          <p className="text-muted-foreground">
            Connect with the sustainability community and share your insights.
          </p>
        </div>

        {/* Post Creator */}
        <PostCreator onPostCreated={handlePostCreated} />

        {/* Filters */}
        <SocialFilters filters={filters} onFiltersChange={handleFiltersChange} />

        {/* Feed */}
        {error ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle size={48} className="mx-auto mb-4 text-destructive" />
              <h3 className="text-lg font-semibold mb-2">Error loading posts</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={refresh}>Try Again</Button>
            </CardContent>
          </Card>
        ) : loading && posts.length === 0 ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex space-x-3 mb-4">
                    <div className="w-10 h-10 bg-muted rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-1/3"></div>
                      <div className="h-3 bg-muted rounded w-1/4"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : posts.length > 0 ? (
          <div>
            {posts.map(post => (
              <SocialPost
                key={post.id}
                post={post}
                onPostUpdate={handlePostUpdate}
                onPostDelete={handlePostDelete}
                onReactionChange={handleReactionChange}
              />
            ))}

            {/* Load More */}
            {hasMore && (
              <div className="text-center mt-6">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  {loading ? 'Loading...' : 'Load More Posts'}
                </Button>
              </div>
            )}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle size={48} className="mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No posts yet</h3>
              <p className="text-muted-foreground mb-4">
                {filters.filter === 'following'
                  ? "Follow some users to see their posts here."
                  : "Be the first to share something with the community!"
                }
              </p>
              {filters.filter === 'following' && (
                <Button
                  variant="outline"
                  onClick={() => setFilters({ ...filters, filter: 'all' })}
                >
                  View All Posts
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SocialFeedPage;
