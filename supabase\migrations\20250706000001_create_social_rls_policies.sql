-- Row Level Security (RLS) Policies for Social System
-- This migration creates security policies for social tables

-- Social Posts Policies
-- Anyone can read non-deleted posts
CREATE POLICY "social_posts_select_policy" ON public.social_posts
    FOR SELECT USING (is_deleted = false);

-- Authenticated users can create posts
CREATE POLICY "social_posts_insert_policy" ON public.social_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own posts
CREATE POLICY "social_posts_update_policy" ON public.social_posts
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Users can delete their own posts (soft delete)
CREATE POLICY "social_posts_delete_policy" ON public.social_posts
    FOR DELETE USING (auth.uid() = user_id);

-- Social Comments Policies
-- Anyone can read non-deleted comments
CREATE POLICY "social_comments_select_policy" ON public.social_comments
    FOR SELECT USING (is_deleted = false);

-- Authenticated users can create comments
CREATE POLICY "social_comments_insert_policy" ON public.social_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own comments
CREATE POLICY "social_comments_update_policy" ON public.social_comments
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Users can delete their own comments (soft delete)
CREATE POLICY "social_comments_delete_policy" ON public.social_comments
    FOR DELETE USING (auth.uid() = user_id);

-- Social Follows Policies
-- Users can see all follow relationships
CREATE POLICY "social_follows_select_policy" ON public.social_follows
    FOR SELECT USING (true);

-- Users can create follow relationships where they are the follower
CREATE POLICY "social_follows_insert_policy" ON public.social_follows
    FOR INSERT WITH CHECK (auth.uid() = follower_id);

-- Users can delete follow relationships where they are the follower
CREATE POLICY "social_follows_delete_policy" ON public.social_follows
    FOR DELETE USING (auth.uid() = follower_id);

-- Social Post Net-Zero Categories Policies
-- Anyone can read post categories
CREATE POLICY "social_post_netzero_categories_select_policy" ON public.social_post_netzero_categories
    FOR SELECT USING (true);

-- Users can add categories to their own posts
CREATE POLICY "social_post_netzero_categories_insert_policy" ON public.social_post_netzero_categories
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.social_posts 
            WHERE id = post_id AND user_id = auth.uid()
        )
    );

-- Users can remove categories from their own posts
CREATE POLICY "social_post_netzero_categories_delete_policy" ON public.social_post_netzero_categories
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.social_posts 
            WHERE id = post_id AND user_id = auth.uid()
        )
    );

-- Social Post Industries Policies
-- Anyone can read post industries
CREATE POLICY "social_post_industries_select_policy" ON public.social_post_industries
    FOR SELECT USING (true);

-- Users can add industries to their own posts
CREATE POLICY "social_post_industries_insert_policy" ON public.social_post_industries
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.social_posts 
            WHERE id = post_id AND user_id = auth.uid()
        )
    );

-- Users can remove industries from their own posts
CREATE POLICY "social_post_industries_delete_policy" ON public.social_post_industries
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.social_posts 
            WHERE id = post_id AND user_id = auth.uid()
        )
    );

-- Grant permissions to authenticated users
GRANT ALL ON TABLE public.social_posts TO authenticated;
GRANT ALL ON TABLE public.social_comments TO authenticated;
GRANT ALL ON TABLE public.social_follows TO authenticated;
GRANT ALL ON TABLE public.social_post_netzero_categories TO authenticated;
GRANT ALL ON TABLE public.social_post_industries TO authenticated;

-- Grant read permissions to anonymous users (for public content)
GRANT SELECT ON TABLE public.social_posts TO anon;
GRANT SELECT ON TABLE public.social_comments TO anon;
GRANT SELECT ON TABLE public.social_follows TO anon;
GRANT SELECT ON TABLE public.social_post_netzero_categories TO anon;
GRANT SELECT ON TABLE public.social_post_industries TO anon;

-- Grant permissions to service role
GRANT ALL ON TABLE public.social_posts TO service_role;
GRANT ALL ON TABLE public.social_comments TO service_role;
GRANT ALL ON TABLE public.social_follows TO service_role;
GRANT ALL ON TABLE public.social_post_netzero_categories TO service_role;
GRANT ALL ON TABLE public.social_post_industries TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.social_posts IS 'User-generated posts in the social feed with category tagging';
COMMENT ON TABLE public.social_comments IS 'Comments and replies on social posts with nested threading support';
COMMENT ON TABLE public.social_follows IS 'User follow relationships for curated social feeds';
COMMENT ON TABLE public.social_post_netzero_categories IS 'Net-zero category tags for social posts';
COMMENT ON TABLE public.social_post_industries IS 'Industry tags for social posts';

COMMENT ON COLUMN public.social_comments.depth IS 'Comment nesting depth: 0=comment, 1=reply, 2=reply to reply, 3=reply to reply to reply';
COMMENT ON COLUMN public.social_comments.thread_path IS 'Dot-separated path for efficient comment tree queries';
COMMENT ON COLUMN public.social_posts.comment_count IS 'Cached count of non-deleted comments for this post';
COMMENT ON COLUMN public.social_posts.is_deleted IS 'Soft delete flag - deleted posts are hidden but preserved';
COMMENT ON COLUMN public.social_comments.is_deleted IS 'Soft delete flag - deleted comments are hidden but preserved';
